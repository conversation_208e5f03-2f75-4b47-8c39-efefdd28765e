const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./web-6ea5MlRL.js","./mui-C1SsAu0U.js","./vendor-B_Ch-B_d.js","./utils-Ch7HAeVX.js","./charts-UhR5A4U7.js","./web-MyPlW46A.js"])))=>i.map(i=>d[i]);
var e,t=Object.defineProperty,n=(e,n,a)=>((e,n,a)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:a}):e[n]=a)(e,"symbol"!=typeof n?n+"":n,a);import{j as a,B as i,A as r,T as s,a as o,b as l,P as c,c as d,d as u,D as m,e as h,L as p,f as x,g,h as y,i as j,I as D,k as S,l as v,M as C,m as b,R as f,n as w,o as E,S as P,p as F,q as U,r as T,N as k,s as R,t as M,C as A,u as I,v as N,w as L,x as O,y as V,z as q,E as B,F as _,G as $,W as z,H as W,J as X,K as Q,O as J,Q as H,U as G,V as Y,X as K,Y as Z,Z as ee,_ as te,$ as ne,a0 as ae,a1 as ie,a2 as re,a3 as se,a4 as oe,a5 as le,a6 as ce,a7 as de,a8 as ue,a9 as me,aa as he,ab as pe,ac as xe,ad as ge,ae as ye,af as je,ag as De,ah as Se,ai as ve,aj as Ce,ak as be,al as fe,am as we,an as Ee,ao as Pe,ap as Fe,aq as Ue,ar as Te,as as ke,at as Re,au as Me,av as Ae,aw as Ie,ax as Ne,ay as Le,az as Oe,aA as Ve,aB as qe,aC as Be,aD as _e,aE as $e,aF as ze,aG as We,aH as Xe,aI as Qe,aJ as Je,aK as He,aL as Ge,aM as Ye,aN as Ke,aO as Ze,aP as et,aQ as tt,aR as nt,aS as at,aT as it,aU as rt,aV as st,aW as ot,aX as lt,aY as ct,aZ as dt,a_ as ut,a$ as mt,b0 as ht,b1 as pt,b2 as xt,b3 as gt,b4 as yt,b5 as jt,b6 as Dt,b7 as St}from"./mui-C1SsAu0U.js";import{c as vt,r as Ct,R as bt,a as ft}from"./vendor-B_Ch-B_d.js";import{b as wt,a as Et,c as Pt,d as Ft,f as Ut,s as Tt,e as kt,g as Rt,i as Mt,h as At,j as It,k as Nt,l as Lt,m as Ot,n as Vt,o as qt,p as Bt,q as _t}from"./utils-Ch7HAeVX.js";import{L as $t,D as zt,B as Wt,C as Xt,a as Qt,b as Jt,P as Ht,c as Gt,d as Yt,p as Kt,e as Zt,f as en,A as tn}from"./charts-UhR5A4U7.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var nn,an,rn={},sn=vt;
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function on(){return on=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},on.apply(this,arguments)}rn.createRoot=sn.createRoot,rn.hydrateRoot=sn.hydrateRoot,(an=nn||(nn={})).Pop="POP",an.Push="PUSH",an.Replace="REPLACE";const ln="popstate";function cn(e){return void 0===e&&(e={}),function(e,t,n,a){void 0===a&&(a={});let{window:i=document.defaultView,v5Compat:r=!1}=a,s=i.history,o=nn.Pop,l=null,c=d();null==c&&(c=0,s.replaceState(on({},s.state,{idx:c}),""));function d(){return(s.state||{idx:null}).idx}function u(){o=nn.Pop;let e=d(),t=null==e?null:e-c;c=e,l&&l({action:o,location:x.location,delta:t})}function m(e,t){o=nn.Push;let a=hn(x.location,e,t);n&&n(a,e),c=d()+1;let u=mn(a,c),m=x.createHref(a);try{s.pushState(u,"",m)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;i.location.assign(m)}r&&l&&l({action:o,location:x.location,delta:1})}function h(e,t){o=nn.Replace;let a=hn(x.location,e,t);n&&n(a,e),c=d();let i=mn(a,c),u=x.createHref(a);s.replaceState(i,"",u),r&&l&&l({action:o,location:x.location,delta:0})}function p(e){let t="null"!==i.location.origin?i.location.origin:i.location.href,n="string"==typeof e?e:pn(e);return n=n.replace(/ $/,"%20"),dn(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}let x={get action(){return o},get location(){return e(i,s)},listen(e){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(ln,u),l=e,()=>{i.removeEventListener(ln,u),l=null}},createHref:e=>t(i,e),createURL:p,encodeLocation(e){let t=p(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:m,replace:h,go:e=>s.go(e)};return x}(function(e,t){let{pathname:n="/",search:a="",hash:i=""}=xn(e.location.hash.substr(1));return n.startsWith("/")||n.startsWith(".")||(n="/"+n),hn("",{pathname:n,search:a,hash:i},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){let n=e.document.querySelector("base"),a="";if(n&&n.getAttribute("href")){let t=e.location.href,n=t.indexOf("#");a=-1===n?t:t.slice(0,n)}return a+"#"+("string"==typeof t?t:pn(t))},function(e,t){un("/"===e.pathname.charAt(0),"relative pathnames are not supported in hash history.push("+JSON.stringify(t)+")")},e)}function dn(e,t){if(!1===e||null==e)throw new Error(t)}function un(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function mn(e,t){return{usr:e.state,key:e.key,idx:t}}function hn(e,t,n,a){return void 0===n&&(n=null),on({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?xn(t):t,{state:n,key:t&&t.key||a||Math.random().toString(36).substr(2,8)})}function pn(e){let{pathname:t="/",search:n="",hash:a=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),a&&"#"!==a&&(t+="#"===a.charAt(0)?a:"#"+a),t}function xn(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let a=e.indexOf("?");a>=0&&(t.search=e.substr(a),e=e.substr(0,a)),e&&(t.pathname=e)}return t}var gn,yn;function jn(e,t,n){return void 0===n&&(n="/"),function(e,t,n){let a="string"==typeof t?xn(t):t,i=Rn(a.pathname||"/",n);if(null==i)return null;let r=Dn(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(r);let s=null;for(let o=0;null==s&&o<r.length;++o){let e=kn(i);s=Un(r[o],e)}return s}(e,t,n)}function Dn(e,t,n,a){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===a&&(a="");let i=(e,i,r)=>{let s={relativePath:void 0===r?e.path||"":r,caseSensitive:!0===e.caseSensitive,childrenIndex:i,route:e};s.relativePath.startsWith("/")&&(dn(s.relativePath.startsWith(a),'Absolute route path "'+s.relativePath+'" nested under path "'+a+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),s.relativePath=s.relativePath.slice(a.length));let o=Nn([a,s.relativePath]),l=n.concat(s);e.children&&e.children.length>0&&(dn(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+o+'".'),Dn(e.children,t,l,o)),(null!=e.path||e.index)&&t.push({path:o,score:Fn(o,e.index),routesMeta:l})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let a of Sn(e.path))i(e,t,a);else i(e,t)}),t}function Sn(e){let t=e.split("/");if(0===t.length)return[];let[n,...a]=t,i=n.endsWith("?"),r=n.replace(/\?$/,"");if(0===a.length)return i?[r,""]:[r];let s=Sn(a.join("/")),o=[];return o.push(...s.map(e=>""===e?r:[r,e].join("/"))),i&&o.push(...s),o.map(t=>e.startsWith("/")&&""===t?"/":t)}(yn=gn||(gn={})).data="data",yn.deferred="deferred",yn.redirect="redirect",yn.error="error";const vn=/^:[\w-]+$/,Cn=3,bn=2,fn=1,wn=10,En=-2,Pn=e=>"*"===e;function Fn(e,t){let n=e.split("/"),a=n.length;return n.some(Pn)&&(a+=En),t&&(a+=bn),n.filter(e=>!Pn(e)).reduce((e,t)=>e+(vn.test(t)?Cn:""===t?fn:wn),a)}function Un(e,t,n){let{routesMeta:a}=e,i={},r="/",s=[];for(let o=0;o<a.length;++o){let e=a[o],n=o===a.length-1,l="/"===r?t:t.slice(r.length)||"/",c=Tn({path:e.relativePath,caseSensitive:e.caseSensitive,end:n},l),d=e.route;if(!c)return null;Object.assign(i,c.params),s.push({params:i,pathname:Nn([r,c.pathname]),pathnameBase:Ln(Nn([r,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(r=Nn([r,c.pathnameBase]))}return s}function Tn(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,a]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);un("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let a=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(a.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(a.push({paramName:"*"}),i+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":""!==e&&"/"!==e&&(i+="(?:(?=\\/|$))");let r=new RegExp(i,t?void 0:"i");return[r,a]}(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let r=i[0],s=r.replace(/(.)\/+$/,"$1"),o=i.slice(1);return{params:a.reduce((e,t,n)=>{let{paramName:a,isOptional:i}=t;if("*"===a){let e=o[n]||"";s=r.slice(0,r.length-e.length).replace(/(.)\/+$/,"$1")}const l=o[n];return e[a]=i&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{}),pathname:r,pathnameBase:s,pattern:e}}function kn(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return un(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function Rn(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,a=e.charAt(n);return a&&"/"!==a?null:e.slice(n)||"/"}function Mn(e,t,n,a){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(a)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function An(e,t){let n=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function In(e,t,n,a){let i;void 0===a&&(a=!1),"string"==typeof e?i=xn(e):(i=on({},e),dn(!i.pathname||!i.pathname.includes("?"),Mn("?","pathname","search",i)),dn(!i.pathname||!i.pathname.includes("#"),Mn("#","pathname","hash",i)),dn(!i.search||!i.search.includes("#"),Mn("#","search","hash",i)));let r,s=""===e||""===i.pathname,o=s?"/":i.pathname;if(null==o)r=n;else{let e=t.length-1;if(!a&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;i.pathname=t.join("/")}r=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:n,search:a="",hash:i=""}="string"==typeof e?xn(e):e,r=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:r,search:On(a),hash:Vn(i)}}(i,r),c=o&&"/"!==o&&o.endsWith("/"),d=(s||"."===o)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!d||(l.pathname+="/"),l}const Nn=e=>e.join("/").replace(/\/\/+/g,"/"),Ln=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),On=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",Vn=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const qn=["post","put","patch","delete"];new Set(qn);const Bn=["get",...qn];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function _n(){return _n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},_n.apply(this,arguments)}new Set(Bn);const $n=Ct.createContext(null),zn=Ct.createContext(null),Wn=Ct.createContext(null),Xn=Ct.createContext(null),Qn=Ct.createContext({outlet:null,matches:[],isDataRoute:!1}),Jn=Ct.createContext(null);function Hn(){return null!=Ct.useContext(Xn)}function Gn(){return Hn()||dn(!1),Ct.useContext(Xn).location}function Yn(e){Ct.useContext(Wn).static||Ct.useLayoutEffect(e)}function Kn(){let{isDataRoute:e}=Ct.useContext(Qn);return e?function(){let{router:e}=function(){let e=Ct.useContext($n);return e||dn(!1),e}(ra.UseNavigateStable),t=oa(sa.UseNavigateStable),n=Ct.useRef(!1);return Yn(()=>{n.current=!0}),Ct.useCallback(function(a,i){void 0===i&&(i={}),n.current&&("number"==typeof a?e.navigate(a):e.navigate(a,_n({fromRouteId:t},i)))},[e,t])}():function(){Hn()||dn(!1);let e=Ct.useContext($n),{basename:t,future:n,navigator:a}=Ct.useContext(Wn),{matches:i}=Ct.useContext(Qn),{pathname:r}=Gn(),s=JSON.stringify(An(i,n.v7_relativeSplatPath)),o=Ct.useRef(!1);return Yn(()=>{o.current=!0}),Ct.useCallback(function(n,i){if(void 0===i&&(i={}),!o.current)return;if("number"==typeof n)return void a.go(n);let l=In(n,JSON.parse(s),r,"path"===i.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:Nn([t,l.pathname])),(i.replace?a.replace:a.push)(l,i.state,i)},[t,a,s,r,e])}()}const Zn=Ct.createContext(null);function ea(e,t){return function(e,t,n,a){Hn()||dn(!1);let{navigator:i}=Ct.useContext(Wn),{matches:r}=Ct.useContext(Qn),s=r[r.length-1],o=s?s.params:{};!s||s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let c,d=Gn();if(t){var u;let e="string"==typeof t?xn(t):t;"/"===l||(null==(u=e.pathname)?void 0:u.startsWith(l))||dn(!1),c=e}else c=d;let m=c.pathname||"/",h=m;if("/"!==l){let e=l.replace(/^\//,"").split("/");h="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let p=jn(e,{pathname:h}),x=function(e,t,n,a){var i;void 0===t&&(t=[]);void 0===n&&(n=null);void 0===a&&(a=null);if(null==e){var r;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(r=a)&&r.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let s=e,o=null==(i=n)?void 0:i.errors;if(null!=o){let e=s.findIndex(e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id]));e>=0||dn(!1),s=s.slice(0,Math.min(s.length,e+1))}let l=!1,c=-1;if(n&&a&&a.v7_partialHydration)for(let d=0;d<s.length;d++){let e=s[d];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=d),e.route.id){let{loaderData:t,errors:a}=n,i=e.route.loader&&void 0===t[e.route.id]&&(!a||void 0===a[e.route.id]);if(e.route.lazy||i){l=!0,s=c>=0?s.slice(0,c+1):[s[0]];break}}}return s.reduceRight((e,a,i)=>{let r,d=!1,u=null,m=null;var h;n&&(r=o&&a.route.id?o[a.route.id]:void 0,u=a.route.errorElement||na,l&&(c<0&&0===i?(la[h="route-fallback"]||(la[h]=!0),d=!0,m=null):c===i&&(d=!0,m=a.route.hydrateFallbackElement||null)));let p=t.concat(s.slice(0,i+1)),x=()=>{let t;return t=r?u:d?m:a.route.Component?Ct.createElement(a.route.Component,null):a.route.element?a.route.element:e,Ct.createElement(ia,{match:a,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(a.route.ErrorBoundary||a.route.errorElement||0===i)?Ct.createElement(aa,{location:n.location,revalidation:n.revalidation,component:u,error:r,children:x(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):x()},null)}(p&&p.map(e=>Object.assign({},e,{params:Object.assign({},o,e.params),pathname:Nn([l,i.encodeLocation?i.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?l:Nn([l,i.encodeLocation?i.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),r,n,a);if(t&&x)return Ct.createElement(Xn.Provider,{value:{location:_n({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:nn.Pop}},x);return x}(e,t)}function ta(){let e=function(){var e;let t=Ct.useContext(Jn),n=function(){let e=Ct.useContext(zn);return e||dn(!1),e}(),a=oa();if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[a]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return Ct.createElement(Ct.Fragment,null,Ct.createElement("h2",null,"Unexpected Application Error!"),Ct.createElement("h3",{style:{fontStyle:"italic"}},t),n?Ct.createElement("pre",{style:a},n):null,null)}const na=Ct.createElement(ta,null);class aa extends Ct.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?Ct.createElement(Qn.Provider,{value:this.props.routeContext},Ct.createElement(Jn.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ia(e){let{routeContext:t,match:n,children:a}=e,i=Ct.useContext($n);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),Ct.createElement(Qn.Provider,{value:t},a)}var ra=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ra||{}),sa=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(sa||{});function oa(e){let t=function(){let e=Ct.useContext(Qn);return e||dn(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||dn(!1),n.route.id}const la={};function ca(e){let{to:t,replace:n,state:a,relative:i}=e;Hn()||dn(!1);let{future:r,static:s}=Ct.useContext(Wn),{matches:o}=Ct.useContext(Qn),{pathname:l}=Gn(),c=Kn(),d=In(t,An(o,r.v7_relativeSplatPath),l,"path"===i),u=JSON.stringify(d);return Ct.useEffect(()=>c(JSON.parse(u),{replace:n,state:a,relative:i}),[c,u,i,n,a]),null}function da(e){return function(e){let t=Ct.useContext(Qn).outlet;return t?Ct.createElement(Zn.Provider,{value:e},t):t}(e.context)}function ua(e){dn(!1)}function ma(e){let{basename:t="/",children:n=null,location:a,navigationType:i=nn.Pop,navigator:r,static:s=!1,future:o}=e;Hn()&&dn(!1);let l=t.replace(/^\/*/,"/"),c=Ct.useMemo(()=>({basename:l,navigator:r,static:s,future:_n({v7_relativeSplatPath:!1},o)}),[l,o,r,s]);"string"==typeof a&&(a=xn(a));let{pathname:d="/",search:u="",hash:m="",state:h=null,key:p="default"}=a,x=Ct.useMemo(()=>{let e=Rn(d,l);return null==e?null:{location:{pathname:e,search:u,hash:m,state:h,key:p},navigationType:i}},[l,d,u,m,h,p,i]);return null==x?null:Ct.createElement(Wn.Provider,{value:c},Ct.createElement(Xn.Provider,{children:n,value:x}))}function ha(e){let{children:t,location:n}=e;return ea(pa(t),n)}function pa(e,t){void 0===t&&(t=[]);let n=[];return Ct.Children.forEach(e,(e,a)=>{if(!Ct.isValidElement(e))return;let i=[...t,a];if(e.type===Ct.Fragment)return void n.push.apply(n,pa(e.props.children,i));e.type!==ua&&dn(!1),e.props.index&&e.props.children&&dn(!1);let r={id:e.props.id||i.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(r.children=pa(e.props.children,i)),n.push(r)}),n}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */new Promise(()=>{});try{window.__reactRouterVersion="6"}catch(br){}const xa=bt.startTransition;function ga(e){let{basename:t,children:n,future:a,window:i}=e,r=Ct.useRef();null==r.current&&(r.current=cn({window:i,v5Compat:!0}));let s=r.current,[o,l]=Ct.useState({action:s.action,location:s.location}),{v7_startTransition:c}=a||{},d=Ct.useCallback(e=>{c&&xa?xa(()=>l(e)):l(e)},[l,c]);return Ct.useLayoutEffect(()=>s.listen(d),[s,d]),Ct.useEffect(()=>{return null==(e=a)||e.v7_startTransition,void(null==e||e.v7_relativeSplatPath);var e},[a]),Ct.createElement(ma,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:s,future:a})}var ya,ja,Da,Sa;(ja=ya||(ya={})).UseScrollRestoration="useScrollRestoration",ja.UseSubmit="useSubmit",ja.UseSubmitFetcher="useSubmitFetcher",ja.UseFetcher="useFetcher",ja.useViewTransitionState="useViewTransitionState",(Sa=Da||(Da={})).UseFetcher="useFetcher",Sa.UseFetchers="useFetchers",Sa.UseScrollRestoration="useScrollRestoration";const va={components:{MuiBreadcrumbs:{defaultProps:{expandText:"Montrer le chemin"}},MuiTablePagination:{defaultProps:{getItemAriaLabel:e=>"first"===e?"Aller à la première page":"last"===e?"Aller à la dernière page":"next"===e?"Aller à la page suivante":"Aller à la page précédente",labelRowsPerPage:"Lignes par page :",labelDisplayedRows:({from:e,to:t,count:n})=>`${e}–${t} sur ${-1!==n?n:`plus que ${t}`}`}},MuiRating:{defaultProps:{getLabelText:e=>`${e} Etoile${1!==e?"s":""}`,emptyLabelText:"Vide"}},MuiAutocomplete:{defaultProps:{clearText:"Vider",closeText:"Fermer",loadingText:"Chargement…",noOptionsText:"Pas de résultats",openText:"Ouvrir"}},MuiAlert:{defaultProps:{closeText:"Fermer"}},MuiPagination:{defaultProps:{"aria-label":"navigation de pagination",getItemAriaLabel:(e,t,n)=>"page"===e?`${n?"":"Aller à la "}page ${t}`:"first"===e?"Aller à la première page":"last"===e?"Aller à la dernière page":"next"===e?"Aller à la page suivante":"Aller à la page précédente"}}}},Ca={},ba=function(e,t,n){let a=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),i=document.querySelector("meta[property=csp-nonce]"),r=(null==i?void 0:i.nonce)||(null==i?void 0:i.getAttribute("nonce"));a=Promise.allSettled(t.map(t=>{if(t=function(e,t){return new URL(e,t).href}(t,n),t in Ca)return;Ca[t]=!0;const a=t.endsWith(".css"),i=a?'[rel="stylesheet"]':"";if(!!n)for(let n=e.length-1;n>=0;n--){const i=e[n];if(i.href===t&&(!a||"stylesheet"===i.rel))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;const s=document.createElement("link");return s.rel=a?"stylesheet":"modulepreload",a||(s.as="script"),s.crossOrigin="",s.href=t,r&&s.setAttribute("nonce",r),document.head.appendChild(s),a?new Promise((e,n)=>{s.addEventListener("load",e),s.addEventListener("error",()=>n(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function i(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return a.then(t=>{for(const e of t||[])"rejected"===e.status&&i(e.reason);return e().catch(i)})};
/*! Capacitor: https://capacitorjs.com/ - MIT License */
var fa,wa;(wa=fa||(fa={})).Unimplemented="UNIMPLEMENTED",wa.Unavailable="UNAVAILABLE";class Ea extends Error{constructor(e,t,n){super(e),this.message=e,this.code=t,this.data=n}}const Pa=e=>{const t=e.CapacitorCustomPlatform||null,n=e.Capacitor||{},a=n.Plugins=n.Plugins||{},i=()=>null!==t?t.name:(e=>{var t,n;return(null==e?void 0:e.androidBridge)?"android":(null===(n=null===(t=null==e?void 0:e.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===n?void 0:n.bridge)?"ios":"web"})(e),r=e=>{var t;return null===(t=n.PluginHeaders)||void 0===t?void 0:t.find(t=>t.name===e)},s=new Map;return n.convertFileSrc||(n.convertFileSrc=e=>e),n.getPlatform=i,n.handleError=t=>e.console.error(t),n.isNativePlatform=()=>"web"!==i(),n.isPluginAvailable=e=>{const t=s.get(e);return!!(null==t?void 0:t.platforms.has(i()))||!!r(e)},n.registerPlugin=(e,o={})=>{const l=s.get(e);if(l)return console.warn(`Capacitor plugin "${e}" already registered. Cannot register plugins twice.`),l.proxy;const c=i(),d=r(e);let u;const m=a=>{let i;const r=(...r)=>{const s=(async()=>(!u&&c in o?u=u="function"==typeof o[c]?await o[c]():o[c]:null!==t&&!u&&"web"in o&&(u=u="function"==typeof o.web?await o.web():o.web),u))().then(t=>{const s=((t,a)=>{var i,r;if(!d){if(t)return null===(r=t[a])||void 0===r?void 0:r.bind(t);throw new Ea(`"${e}" plugin is not implemented on ${c}`,fa.Unimplemented)}{const r=null==d?void 0:d.methods.find(e=>a===e.name);if(r)return"promise"===r.rtype?t=>n.nativePromise(e,a.toString(),t):(t,i)=>n.nativeCallback(e,a.toString(),t,i);if(t)return null===(i=t[a])||void 0===i?void 0:i.bind(t)}})(t,a);if(s){const e=s(...r);return i=null==e?void 0:e.remove,e}throw new Ea(`"${e}.${a}()" is not implemented on ${c}`,fa.Unimplemented)});return"addListener"===a&&(s.remove=async()=>i()),s};return r.toString=()=>`${a.toString()}() { [capacitor code] }`,Object.defineProperty(r,"name",{value:a,writable:!1,configurable:!1}),r},h=m("addListener"),p=m("removeListener"),x=(e,t)=>{const n=h({eventName:e},t),a=async()=>{const a=await n;p({eventName:e,callbackId:a},t)},i=new Promise(e=>n.then(()=>e({remove:a})));return i.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await a()},i},g=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return d?x:h;case"removeListener":return p;default:return m(t)}}});return a[e]=g,s.set(e,{name:e,proxy:g,platforms:new Set([...Object.keys(o),...d?[c]:[]])}),g},n.Exception=Ea,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},Fa=(e=>e.Capacitor=Pa(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),Ua=Fa.registerPlugin;class Ta{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let n=!1;this.listeners[e]||(this.listeners[e]=[],n=!0),this.listeners[e].push(t);const a=this.windowListeners[e];a&&!a.registered&&this.addWindowListener(a),n&&this.sendRetainedArgumentsForEvent(e);return Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,n){const a=this.listeners[e];if(a)a.forEach(e=>e(t));else if(n){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}}hasListeners(e){var t;return!!(null===(t=this.listeners[e])||void 0===t?void 0:t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new Fa.Exception(e,fa.Unimplemented)}unavailable(e="not available"){return new Fa.Exception(e,fa.Unavailable)}async removeListener(e,t){const n=this.listeners[e];if(!n)return;const a=n.indexOf(t);this.listeners[e].splice(a,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(t=>{this.notifyListeners(e,t)}))}}const ka=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),Ra=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class Ma extends Ta{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach(e=>{if(e.length<=0)return;let[n,a]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");n=Ra(n).trim(),a=Ra(a).trim(),t[n]=a}),t}async setCookie(e){try{const t=ka(e.key),n=ka(e.value),a=`; expires=${(e.expires||"").replace("expires=","")}`,i=(e.path||"/").replace("path=",""),r=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${n||""}${a}; path=${i}; ${r};`}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}Ua("CapacitorCookies",{web:()=>new Ma});const Aa=(e,t={})=>{const n=Object.assign({method:e.method||"GET",headers:e.headers},t),a=((e={})=>{const t=Object.keys(e);return Object.keys(e).map(e=>e.toLocaleLowerCase()).reduce((n,a,i)=>(n[a]=e[t[i]],n),{})})(e.headers)["content-type"]||"";if("string"==typeof e.data)n.body=e.data;else if(a.includes("application/x-www-form-urlencoded")){const t=new URLSearchParams;for(const[n,a]of Object.entries(e.data||{}))t.set(n,a);n.body=t.toString()}else if(a.includes("multipart/form-data")||e.data instanceof FormData){const t=new FormData;if(e.data instanceof FormData)e.data.forEach((e,n)=>{t.append(n,e)});else for(const n of Object.keys(e.data))t.append(n,e.data[n]);n.body=t;const a=new Headers(n.headers);a.delete("content-type"),n.headers=a}else(a.includes("application/json")||"object"==typeof e.data)&&(n.body=JSON.stringify(e.data));return n};class Ia extends Ta{async request(e){const t=Aa(e,e.webFetchExtra),n=((e,t=!0)=>e?Object.entries(e).reduce((e,n)=>{const[a,i]=n;let r,s;return Array.isArray(i)?(s="",i.forEach(e=>{r=t?encodeURIComponent(e):e,s+=`${a}=${r}&`}),s.slice(0,-1)):(r=t?encodeURIComponent(i):i,s=`${a}=${r}`),`${e}&${s}`},"").substr(1):null)(e.params,e.shouldEncodeUrlParams),a=n?`${e.url}?${n}`:e.url,i=await fetch(a,t),r=i.headers.get("content-type")||"";let s,o,{responseType:l="text"}=i.ok?e:{};switch(r.includes("application/json")&&(l="json"),l){case"arraybuffer":case"blob":o=await i.blob(),s=await(async e=>new Promise((t,n)=>{const a=new FileReader;a.onload=()=>{const e=a.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},a.onerror=e=>n(e),a.readAsDataURL(e)}))(o);break;case"json":s=await i.json();break;default:s=await i.text()}const c={};return i.headers.forEach((e,t)=>{c[t]=e}),{data:s,headers:c,status:i.status,url:i.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}Ua("CapacitorHttp",{web:()=>new Ia});const Na=()=>{const e=Fa.getPlatform();return{isMobile:Fa.isNativePlatform(),isDesktop:!Fa.isNativePlatform(),isAndroid:"android"===e,isIOS:"ios"===e,isWeb:"web"===e,platform:e}},La=()=>Fa.isNativePlatform();class Oa{static arrayToCSV(e,t,n=!0){if(!e||0===e.length){const e=t.map(e=>e.header).join(",")+"\n";return n?"\ufeff"+e:e}const a=[t.map(e=>e.header).join(","),...e.map(e=>t.map(t=>{let n=e[t.key];return null==n?n="":"date"===t.type&&n?n=new Date(n).toISOString().split("T")[0]:"boolean"===t.type?n=n?"Oui":"Non":"object"==typeof n&&(n=Oa.formatComplexValue(n,t.key)),n=String(n),(n.includes(",")||n.includes('"')||n.includes("\n"))&&(n='"'+n.replace(/"/g,'""')+'"'),n}).join(","))].join("\n");return n?"\ufeff"+a:a}static formatComplexValue(e,t){if(!e)return"";if("paiements"===t&&Array.isArray(e))return 0===e.length?"Aucun paiement":e.map((e,t)=>{const n=[];if(n.push(`Paiement ${t+1}:`),e.montantCDF&&n.push(`${e.montantCDF.toLocaleString("fr-FR")} CDF`),e.montantUSD&&n.push(`${e.montantUSD.toLocaleString("fr-FR",{minimumFractionDigits:2})} USD`),e.methodePaiement){const t={cash:"Comptant",mobile_money:"Mobile Money",bank:"Banque",card:"Carte"};n.push(`via ${t[e.methodePaiement]||e.methodePaiement}`)}if(e.datePaiement){const t=new Date(e.datePaiement);n.push(`le ${t.toLocaleDateString("fr-FR")}`)}return e.notes&&n.push(`(${e.notes})`),n.join(" ")}).join(" | ");if("produits"===t&&Array.isArray(e))return 0===e.length?"Aucun produit":e.map((e,t)=>{const n=[];return e.nom&&n.push(`${e.nom}`),e.quantite&&n.push(`(Qté: ${e.quantite})`),e.prixUnitaireCDF&&n.push(`${e.prixUnitaireCDF.toLocaleString("fr-FR")} CDF/unité`),n.join(" ")}).join(" | ");if(Array.isArray(e))return e.length>0?`${e.length} élément(s)`:"Aucun élément";if("object"==typeof e){const t=Object.keys(e);if(0===t.length)return"Objet vide";return t.slice(0,3).map(t=>{const n=e[t];return null!=n&&""!==n?`${t}: ${String(n).substring(0,20)}`:null}).filter(Boolean).join(", ")||"Données complexes"}return JSON.stringify(e)}static createExcelCompatibleBlob(e){return new Blob([e],{type:"text/csv;charset=utf-8"})}static downloadCSV(e,t,n){const a=Oa.arrayToCSV(e,t,!0),i=Oa.createExcelCompatibleBlob(a),r=URL.createObjectURL(i),s=document.createElement("a");s.href=r,s.download=n,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(r)}static csvToArray(e,t){if(!e||""===e.trim())return[];const n=this.parseCSVLines(e);if(n.length<=1)return[];return n.slice(1).map((e,n)=>{const a=e,i={};return t.forEach((e,t)=>{let n=a[t]||"";"number"===e.type?n=""===n?0:parseFloat(n)||0:"boolean"===e.type?n="oui"===n.toLowerCase()||"true"===n.toLowerCase()||"1"===n:"date"===e.type&&n?n=new Date(n).toISOString():e.key.includes("prix")&&"string"==typeof n&&(n=parseFloat(n)||0),i[e.key]=n}),i.id||(i.id=String(n+1)),i})}static parseCSVLines(e){const t=[],n=e.split("\n");for(const a of n){if(""===a.trim())continue;const e=[];let n="",i=!1,r=0;for(;r<a.length;){const t=a[r];'"'===t?i&&'"'===a[r+1]?(n+='"',r+=2):(i=!i,r++):","!==t||i?(n+=t,r++):(e.push(n),n="",r++)}e.push(n),t.push(e)}return t}static validateCSVData(e,t){const n=[];return e.forEach((e,a)=>{t.forEach(t=>{!t.required||void 0!==e[t.key]&&null!==e[t.key]&&""!==e[t.key]||n.push(`Ligne ${a+2}: Le champ "${t.header}" est requis`),"number"===t.type&&void 0!==e[t.key]&&isNaN(Number(e[t.key]))&&n.push(`Ligne ${a+2}: Le champ "${t.header}" doit être un nombre`)})}),{isValid:0===n.length,errors:n}}static generateTemplate(e){const t=[{}];return e.forEach(e=>{switch(e.type){case"string":t[0][e.key]="Exemple";break;case"number":t[0][e.key]=100;break;case"boolean":t[0][e.key]=!0;break;case"date":t[0][e.key]=(new Date).toISOString()}}),this.arrayToCSV(t,e)}}const Va=[{key:"id",header:"ID",type:"string",required:!0},{key:"nom",header:"Nom du Produit",type:"string",required:!0},{key:"description",header:"Description",type:"string"},{key:"prixAchatCDF",header:"Prix d'achat CDF",type:"number",required:!0},{key:"prixAchatUSD",header:"Prix d'achat USD",type:"number"},{key:"prixCDF",header:"Prix de vente CDF",type:"number",required:!0},{key:"prixUSD",header:"Prix de vente USD",type:"number"},{key:"beneficeUnitaireCDF",header:"Bénéfice unitaire CDF",type:"number"},{key:"beneficeUnitaireUSD",header:"Bénéfice unitaire USD",type:"number"},{key:"codeQR",header:"Code QR",type:"string"},{key:"categorie",header:"Catégorie",type:"string",required:!0},{key:"stock",header:"Stock",type:"number",required:!0},{key:"stockMin",header:"Stock Minimum",type:"number"},{key:"codeBarres",header:"Code Barres",type:"string"},{key:"dateCreation",header:"Date de Création",type:"date"},{key:"dateModification",header:"Date de Modification",type:"date"}],qa=[{key:"id",header:"ID",type:"string",required:!0},{key:"nom",header:"Nom",type:"string",required:!0},{key:"email",header:"Email",type:"string",required:!0},{key:"role",header:"Rôle",type:"string",required:!0},{key:"motDePasse",header:"Mot de Passe",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date"},{key:"actif",header:"Actif",type:"boolean"}],Ba=[{key:"id",header:"ID",type:"string",required:!0},{key:"date",header:"Date",type:"date",required:!0},{key:"client",header:"Client",type:"string"},{key:"produits",header:"Produits (JSON)",type:"string",required:!0},{key:"totalCDF",header:"Total CDF",type:"number",required:!0},{key:"totalUSD",header:"Total USD",type:"number",required:!0},{key:"typePaiement",header:"Type de Paiement",type:"string",required:!0},{key:"typeVente",header:"Type de Vente",type:"string",required:!0},{key:"vendeur",header:"Vendeur",type:"string",required:!0},{key:"numeroRecu",header:"Numéro de Reçu",type:"string"}],_a=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomClient",header:"Client",type:"string",required:!0},{key:"telephoneClient",header:"Téléphone",type:"string"},{key:"adresseClient",header:"Adresse",type:"string"},{key:"montantTotalCDF",header:"Montant Total CDF",type:"number",required:!0},{key:"montantTotalUSD",header:"Montant Total USD",type:"number"},{key:"montantPayeCDF",header:"Montant Payé CDF",type:"number",required:!0},{key:"montantPayeUSD",header:"Montant Payé USD",type:"number"},{key:"montantRestantCDF",header:"Montant Restant CDF",type:"number",required:!0},{key:"montantRestantUSD",header:"Montant Restant USD",type:"number"},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateEcheance",header:"Date d'Échéance",type:"date"},{key:"statut",header:"Statut",type:"string",required:!0},{key:"statutPaiement",header:"Statut de Paiement",type:"string",required:!0},{key:"venteId",header:"ID de Vente",type:"string"},{key:"paiements",header:"Paiements",type:"string"},{key:"notes",header:"Notes",type:"string"}],$a=[{key:"id",header:"ID",type:"string",required:!0},{key:"description",header:"Description",type:"string",required:!0},{key:"montantCDF",header:"Montant CDF",type:"number",required:!0},{key:"montantUSD",header:"Montant USD",type:"number",required:!0},{key:"date",header:"Date",type:"date",required:!0},{key:"categorie",header:"Catégorie",type:"string",required:!0},{key:"utilisateur",header:"Utilisateur",type:"string",required:!0},{key:"numeroRecu",header:"Numéro de Reçu",type:"string"}],za=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomComplet",header:"Nom Complet",type:"string",required:!0},{key:"poste",header:"Poste",type:"string",required:!0},{key:"salaireCDF",header:"Salaire CDF",type:"number",required:!0},{key:"salaireUSD",header:"Salaire USD",type:"number"},{key:"dateEmbauche",header:"Date d'Embauche",type:"date",required:!0},{key:"telephone",header:"Téléphone",type:"string"},{key:"adresse",header:"Adresse",type:"string"},{key:"statut",header:"Statut",type:"string",required:!0},{key:"notes",header:"Notes",type:"string"},{key:"creePar",header:"Créé par",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateModification",header:"Date de Modification",type:"date"}],Wa=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomEmploye",header:"Nom Employé",type:"string",required:!0},{key:"montantCDF",header:"Montant CDF",type:"number",required:!0},{key:"montantUSD",header:"Montant USD",type:"number"},{key:"datePaiement",header:"Date de Paiement",type:"date",required:!0},{key:"methodePaiement",header:"Méthode de Paiement",type:"string",required:!0},{key:"notes",header:"Notes",type:"string"},{key:"creePar",header:"Créé par",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateModification",header:"Date de Modification",type:"date"}],Xa=[{key:"cle",header:"Clé",type:"string",required:!0},{key:"valeur",header:"Valeur",type:"string",required:!0},{key:"type",header:"Type",type:"string",required:!0},{key:"description",header:"Description",type:"string"}];function Qa(e){var t,n;const a=[];return a.push({cle:"tauxChangeUSDCDF",valeur:(null==(t=e.tauxChangeUSDCDF)?void 0:t.toString())||"2800",type:"number",description:"Taux de change USD vers CDF"}),a.push({cle:"seuilStockBas",valeur:(null==(n=e.seuilStockBas)?void 0:n.toString())||"10",type:"number",description:"Seuil de stock bas"}),e.categories&&Array.isArray(e.categories)&&a.push({cle:"categories",valeur:JSON.stringify(e.categories),type:"json",description:"Catégories de produits"}),e.entreprise&&a.push({cle:"entreprise",valeur:JSON.stringify(e.entreprise),type:"json",description:"Informations de l'entreprise"}),e.impression&&a.push({cle:"impression",valeur:JSON.stringify(e.impression),type:"json",description:"Paramètres d'impression des reçus"}),a}const Ja=new class{constructor(){n(this,"prefix","smartboutique_csv_")}getKey(e){return`${this.prefix}${e}`}setCSV(e,t,n){try{const a=Oa.arrayToCSV(t,n);localStorage.setItem(this.getKey(e),a)}catch(a){console.error(`Erreur lors de la sauvegarde CSV ${e}:`,a)}}getCSV(e,t,n=[]){try{const a=localStorage.getItem(this.getKey(e));return a?Oa.csvToArray(a,t):n}catch(a){return console.error(`Erreur lors de la lecture CSV ${e}:`,a),n}}set(e,t){try{const n=JSON.stringify(t);localStorage.setItem(this.getKey(e),n)}catch(n){console.error("Erreur lors de la sauvegarde:",n)}}get(e,t){try{const n=localStorage.getItem(this.getKey(e));return null===n?t:JSON.parse(n)}catch(n){return console.error("Erreur lors de la lecture:",n),t}}remove(e){localStorage.removeItem(this.getKey(e))}clear(){Object.keys(localStorage).forEach(e=>{e.startsWith(this.prefix)&&localStorage.removeItem(e)})}getUsers(){return this.getCSV("users",qa,[])}setUsers(e){this.setCSV("users",e,qa)}getProducts(){return this.getCSV("products",Va,[]).map(e=>{if(!e.prixAchatCDF&&e.prixCDF&&(e.prixAchatCDF=.7*e.prixCDF),e.prixAchatCDF=Number(e.prixAchatCDF)||0,e.prixAchatUSD=e.prixAchatUSD?Number(e.prixAchatUSD):void 0,e.prixCDF=Number(e.prixCDF)||0,e.prixUSD=e.prixUSD?Number(e.prixUSD):void 0,!e.beneficeUnitaireCDF||isNaN(Number(e.beneficeUnitaireCDF))?e.beneficeUnitaireCDF=e.prixCDF-e.prixAchatCDF:e.beneficeUnitaireCDF=Number(e.beneficeUnitaireCDF),!e.beneficeUnitaireUSD||isNaN(Number(e.beneficeUnitaireUSD))){const t=2800;e.beneficeUnitaireUSD=Math.round(e.beneficeUnitaireCDF/t*100)/100}else e.beneficeUnitaireUSD=Number(e.beneficeUnitaireUSD);e.stock=Number(e.stock)||0,e.stockMin=Number(e.stockMin)||0;const t=(new Date).toISOString();return e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())||(e.dateCreation=t),e.dateModification&&!isNaN(new Date(e.dateModification).getTime())||(e.dateModification=t),e})}setProducts(e){this.setCSV("products",e,Va)}getSales(){try{const e=this.getCSV("sales",Ba,[]);return e.filter(e=>e&&"object"==typeof e).map(e=>{const t={...e,datevente:e.date||e.datevente||(new Date).toISOString(),methodePaiement:e.typePaiement||e.methodePaiement||"cash",nomClient:e.client||e.nomClient||"Client"};t.totalCDF=Number(t.totalCDF)||0,t.totalUSD=t.totalUSD?Number(t.totalUSD):void 0;let n=t.produits||[];if("string"==typeof n)try{n=JSON.parse(n)}catch(a){console.warn("Error parsing produits JSON:",a),n=[]}return Array.isArray(n)?t.produits=n.map(e=>({...e,quantite:Number(e.quantite)||0,prixUnitaireCDF:Number(e.prixUnitaireCDF)||0,prixUnitaireUSD:e.prixUnitaireUSD?Number(e.prixUnitaireUSD):void 0,totalCDF:Number(e.totalCDF)||0,totalUSD:e.totalUSD?Number(e.totalUSD):void 0})):t.produits=[],t})}catch(e){return console.error("Error getting sales data:",e),[]}}setSales(e){const t=e.map(e=>({...e,date:e.datevente||e.date,typePaiement:e.methodePaiement||e.typePaiement,client:e.nomClient||e.client,produits:"string"==typeof e.produits?e.produits:JSON.stringify(e.produits||[])}));this.setCSV("sales",t,Ba)}getDebts(){return this.getCSV("debts",_a,[]).map(e=>{e.statutPaiement||(e.statutPaiement="paid"===e.statut?"paye":"impaye"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantTotalUSD=e.montantTotalUSD?Number(e.montantTotalUSD):void 0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantPayeUSD=e.montantPayeUSD?Number(e.montantPayeUSD):void 0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,e.montantRestantUSD=e.montantRestantUSD?Number(e.montantRestantUSD):void 0;let t=e.paiements||[];if("string"==typeof t)try{t=JSON.parse(t)}catch(n){console.warn("Error parsing paiements JSON:",n),t=[]}return Array.isArray(t)?e.paiements=t.map(e=>({...e,montantCDF:Number(e.montantCDF)||0,montantUSD:e.montantUSD?Number(e.montantUSD):void 0})):e.paiements=[],e})}setDebts(e){this.setCSV("debts",e,_a)}getDettes(){return this.getDebts()}setDettes(e){this.setDebts(e)}getCreances(){return this.getDebts()}setCreances(e){this.setDebts(e)}getExpenses(){return this.getCSV("expenses",$a,[])}setExpenses(e){this.setCSV("expenses",e,$a)}getEmployeePayments(){return this.getCSV("employee_payments",Wa,[])}setEmployeePayments(e){this.setCSV("employee_payments",e,Wa)}addEmployeePayment(e){const t=this.getEmployeePayments();t.push(e),this.setEmployeePayments(t)}updateEmployeePayment(e){const t=this.getEmployeePayments(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,this.setEmployeePayments(t))}deleteEmployeePayment(e){const t=this.getEmployeePayments().filter(t=>t.id!==e);this.setEmployeePayments(t)}getEmployees(){return this.getCSV("employees",za,[])}setEmployees(e){this.setCSV("employees",e,za)}addEmployee(e){const t=this.getEmployees();t.push(e),this.setEmployees(t)}updateEmployee(e){const t=this.getEmployees(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,this.setEmployees(t))}deleteEmployee(e){const t=this.getEmployees().filter(t=>t.id!==e);this.setEmployees(t)}getSettings(){return this.get("settings",{tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Électronique",description:"Appareils électroniques",couleur:"#2196F3"},{id:"2",nom:"Vêtements",description:"Vêtements et accessoires",couleur:"#4CAF50"},{id:"3",nom:"Alimentation",description:"Produits alimentaires",couleur:"#FF9800"},{id:"4",nom:"Maison",description:"Articles pour la maison",couleur:"#9C27B0"},{id:"5",nom:"Beauté",description:"Produits de beauté",couleur:"#E91E63"},{id:"6",nom:"Boissons",description:"Boissons et breuvages",couleur:"#00BCD4"},{id:"7",nom:"Épicerie",description:"Produits d'épicerie",couleur:"#795548"},{id:"8",nom:"Livres",description:"Livres et éducation",couleur:"#607D8B"},{id:"9",nom:"Sport",description:"Articles de sport",couleur:"#FF5722"},{id:"10",nom:"Santé",description:"Produits de santé",couleur:"#8BC34A"}],entreprise:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+*********** 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}})}setSettings(e){this.set("settings",e)}getCurrentUser(){return this.get("currentUser",null)}setCurrentUser(e){this.set("currentUser",e)}initializeDefaultData(){if(0===this.getUsers().length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];this.setUsers(e)}0===this.getProducts().length&&this.initializeProductCatalog();0===this.getSales().length&&this.initializeSampleSales();0===this.getDebts().length&&this.initializeSampleDebts()}clearAllData(){localStorage.clear(),console.log("All localStorage data cleared")}initializeProductCatalog(){const e=[{id:"1",nom:"iPhone 15",description:"Smartphone Apple iPhone 15 128GB",prixAchatCDF:168e4,prixAchatUSD:600,prixCDF:224e4,prixUSD:800,beneficeUnitaireCDF:56e4,beneficeUnitaireUSD:200,codeQR:"SB12345678ABCD",categorie:"Électronique",stock:25,stockMin:5,codeBarres:"1234567890123",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"2",nom:"T-shirt Nike",description:"T-shirt Nike en coton, taille M",prixAchatCDF:7e4,prixAchatUSD:25,prixCDF:98e3,prixUSD:35,beneficeUnitaireCDF:28e3,beneficeUnitaireUSD:10,codeQR:"SB12345679EFGH",categorie:"Vêtements",stock:50,stockMin:10,codeBarres:"1234567890124",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"3",nom:"Café Arabica",description:"Café Arabica premium 500g",prixAchatCDF:25200,prixAchatUSD:9,prixCDF:33600,prixUSD:12,beneficeUnitaireCDF:8400,beneficeUnitaireUSD:3,codeQR:"SB12345680IJKL",categorie:"Alimentation",stock:8,stockMin:15,codeBarres:"1234567890125",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"4",nom:"Sucre",description:"Sucre blanc cristallisé 1kg",prixAchatCDF:6300,prixAchatUSD:2.25,prixCDF:8400,prixUSD:3,beneficeUnitaireCDF:2100,beneficeUnitaireUSD:.75,codeQR:"SB12345681MNOP",categorie:"Épicerie",stock:120,stockMin:20,codeBarres:"1234567890126",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"5",nom:"Riz",description:"Riz blanc parfumé 5kg",prixAchatCDF:33600,prixAchatUSD:12,prixCDF:42e3,prixUSD:15,beneficeUnitaireCDF:8400,beneficeUnitaireUSD:3,codeQR:"SB12345682QRST",categorie:"Alimentation",stock:80,stockMin:15,codeBarres:"1234567890127",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"6",nom:"Sel",description:"Sel de cuisine iodé 500g",prixCDF:2800,prixUSD:1,codeQR:"SB12345683UVWX",categorie:"Épicerie",stock:200,stockMin:30,codeBarres:"1234567890128",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"7",nom:"Lait",description:"Lait entier UHT 1 litre",prixCDF:5600,prixUSD:2,codeQR:"SB12345684YZAB",categorie:"Boissons",stock:60,stockMin:12,codeBarres:"1234567890129",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"8",nom:"Thé",description:"Thé noir en sachets, boîte de 25",prixCDF:11200,prixUSD:4,codeQR:"SB12345685CDEF",categorie:"Boissons",stock:45,stockMin:10,codeBarres:"1234567890130",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"9",nom:"Vestes",description:"Veste en jean unisexe, taille L",prixCDF:14e4,prixUSD:50,codeQR:"SB12345686GHIJ",categorie:"Vêtements",stock:30,stockMin:5,codeBarres:"1234567890131",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"10",nom:"Livres",description:'Roman français "Le Petit Prince"',prixCDF:22400,prixUSD:8,codeQR:"SB12345687KLMN",categorie:"Livres",stock:25,stockMin:5,codeBarres:"1234567890132",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()}];this.setProducts(e)}initializeSampleSales(){const e=new Date,t=new Date(e.getTime()-864e5),n=new Date(e.getTime()-1728e5),a=[{id:"1",datevente:t.toISOString(),nomClient:"Jean Mukendi",telephoneClient:"+243 900 000 001",produits:[{produitId:"1",nomProduit:"iPhone 15",quantite:1,prixUnitaireCDF:224e4,prixUnitaireUSD:800,totalCDF:224e4,totalUSD:800}],totalCDF:224e4,totalUSD:800,methodePaiement:"cash",typeVente:"cash",vendeur:"Super Admin",notes:"Vente comptant"},{id:"2",datevente:t.toISOString(),nomClient:"Marie Kabila",telephoneClient:"+243 900 000 002",produits:[{produitId:"2",nomProduit:"T-shirt Nike",quantite:2,prixUnitaireCDF:98e3,prixUnitaireUSD:35,totalCDF:196e3,totalUSD:70},{produitId:"4",nomProduit:"Sucre",quantite:3,prixUnitaireCDF:8400,prixUnitaireUSD:3,totalCDF:25200,totalUSD:9}],totalCDF:221200,totalUSD:79,methodePaiement:"mobile_money",typeVente:"cash",vendeur:"Gestionnaire",notes:"Paiement mobile money"},{id:"3",datevente:n.toISOString(),nomClient:"Paul Tshisekedi",telephoneClient:"+243 900 000 003",produits:[{produitId:"5",nomProduit:"Riz",quantite:2,prixUnitaireCDF:42e3,prixUnitaireUSD:15,totalCDF:84e3,totalUSD:30}],totalCDF:84e3,totalUSD:30,methodePaiement:"card",typeVente:"cash",vendeur:"Employé",notes:"Paiement par carte"}];this.setSales(a)}initializeSampleDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()-12096e5),a=new Date(e.getTime()+2592e6),i=new Date(e.getTime()+12096e5),r=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:a.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 998 765 432",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:25e4,montantTotalUSD:89.29,montantPayeCDF:1e5,montantPayeUSD:35.71,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:n.toISOString(),dateEcheance:i.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:1e5,montantUSD:35.71,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e5).toISOString(),notes:"Paiement partiel en espèces"}],notes:"Paiement partiel effectué - Vêtements et accessoires"},{id:"DET-003",venteId:"VTE-CREDIT-003",nomClient:"Joseph Kabila Kabange",telephoneClient:"+243 811 222 333",adresseClient:"Avenue de la Paix, Lubumbashi",montantTotalCDF:18e4,montantTotalUSD:64.29,montantPayeCDF:18e4,montantPayeUSD:64.29,montantRestantCDF:0,montantRestantUSD:0,dateCreation:n.toISOString(),dateEcheance:a.toISOString(),statut:"paid",statutPaiement:"paye",paiements:[{id:"PAY-002",montantCDF:18e4,montantUSD:64.29,methodePaiement:"mobile_money",datePaiement:new Date(e.getTime()-1728e5).toISOString(),notes:"Paiement complet via Mobile Money"}],notes:"Dette entièrement payée - Alimentation et boissons"},{id:"DET-004",venteId:"VTE-CREDIT-004",nomClient:"Fatou Diallo Sankara",telephoneClient:"+243 977 888 999",adresseClient:"Quartier Matonge, Kinshasa",montantTotalCDF:32e4,montantTotalUSD:114.29,montantPayeCDF:8e4,montantPayeUSD:28.57,montantRestantCDF:24e4,montantRestantUSD:85.71,dateCreation:new Date(e.getTime()-3888e6).toISOString(),dateEcheance:new Date(e.getTime()-1296e6).toISOString(),statut:"overdue",statutPaiement:"impaye",paiements:[{id:"PAY-003",montantCDF:8e4,montantUSD:28.57,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e6).toISOString(),notes:"Paiement partiel initial"}],notes:"Dette en retard - Nécessite suivi"}];this.setDebts(r)}exportData(){const e=this.getUsers(),t=this.getProducts(),n=this.getSales(),a=this.getDebts(),i=this.getExpenses(),r=this.getEmployeePayments(),s=this.getSettings(),o={exportDate:(new Date).toISOString(),products:Oa.arrayToCSV(t,Va),users:Oa.arrayToCSV(e,qa),sales:Oa.arrayToCSV(n,Ba),debts:Oa.arrayToCSV(a,_a),expenses:Oa.arrayToCSV(i,$a),employeePayments:Oa.arrayToCSV(r,Wa),settings:Oa.arrayToCSV(Qa(s),Xa)};return{csvData:`SmartBoutique - Sauvegarde Complète (Desktop)\nDate d'exportation: ${o.exportDate}\n\n=== PRODUITS ===\n${o.products}\n\n=== UTILISATEURS ===\n${o.users}\n\n=== VENTES ===\n${o.sales}\n\n=== DETTES ===\n${o.debts}\n\n=== DÉPENSES ===\n${o.expenses}\n\n=== PAIEMENTS EMPLOYÉS ===\n${o.employeePayments}\n\n=== PARAMÈTRES ===\n${o.settings}\n`,exportDate:o.exportDate}}importData(e){try{return e.csvData&&"string"==typeof e.csvData?this.importFromCSVBackup(e.csvData):(e.users&&this.setUsers(e.users),e.products&&this.setProducts(e.products),e.sales&&this.setSales(e.sales),e.debts&&this.setDebts(e.debts),e.expenses&&this.setExpenses(e.expenses),e.employeePayments&&this.setEmployeePayments(e.employeePayments),e.settings&&this.setSettings(e.settings),!0)}catch(t){return console.error("Erreur lors de l'importation:",t),!1}}importFromCSVBackup(e){try{const t=this.parseCSVBackup(e);if(t.products){const e=Oa.csvToArray(t.products,Va);this.setProducts(e)}if(t.users){const e=Oa.csvToArray(t.users,qa);this.setUsers(e)}if(t.sales){const e=Oa.csvToArray(t.sales,Ba);this.setSales(e)}if(t.debts){const e=Oa.csvToArray(t.debts,_a);this.setDebts(e)}if(t.expenses){const e=Oa.csvToArray(t.expenses,$a);this.setExpenses(e)}if(t.employeePayments){const e=Oa.csvToArray(t.employeePayments,Wa);this.setEmployeePayments(e)}if(t.settings){const e=function(e){const t={};return e.forEach(e=>{const n=e.cle;let a=e.valeur;switch(e.type){case"number":a=parseFloat(a)||0;break;case"boolean":a="true"===a||"1"===a||"Oui"===a;break;case"json":try{a=JSON.parse(a)}catch(i){console.error(`Erreur lors du parsing JSON pour ${n}:`,i),a=null}}t[n]=a}),t}(Oa.csvToArray(t.settings,Xa));this.setSettings(e)}return!0}catch(t){return console.error("Erreur lors de l'importation CSV:",t),!1}}parseCSVBackup(e){const t={},n=e.split("\n");let a="",i=[];for(const r of n)if(r.startsWith("=== ")&&r.endsWith(" ===")){a&&i.length>0&&(t[a]=i.join("\n"));switch(r.replace(/=== | ===/g,"").toLowerCase()){case"produits":a="products";break;case"utilisateurs":a="users";break;case"ventes":a="sales";break;case"dettes":a="debts";break;case"dépenses":a="expenses";break;case"paiements employés":a="employeePayments";break;case"paramètres":a="settings";break;default:a=""}i=[]}else a&&""!==r.trim()&&i.push(r);return a&&i.length>0&&(t[a]=i.join("\n")),t}exportCSV(e){let t=[],n=[];switch(e){case"products":t=this.getProducts(),n=Va;break;case"users":t=this.getUsers(),n=qa;break;case"sales":t=this.getSales(),n=Ba;break;case"debts":t=this.getDebts(),n=_a;break;case"expenses":t=this.getExpenses(),n=$a;break;case"employee_payments":t=this.getEmployeePayments(),n=Wa}return Oa.arrayToCSV(t,n)}importCSV(e,t,n=!1){try{let a=[],i=[];switch(e){case"products":a=Va,i=n?[]:this.getProducts();break;case"users":a=qa,i=n?[]:this.getUsers();break;default:return{success:!1,message:"Type de données non supporté",errors:[]}}const r=Oa.csvToArray(t,a),s=Oa.validateCSVData(r,a);if(!s.isValid)return{success:!1,message:"Données invalides",errors:s.errors};let o=r;if(!n&&i.length>0){const e=new Set(i.map(e=>e.id)),t=r.filter(t=>!e.has(t.id));o=[...i,...t]}switch(e){case"products":this.setProducts(o);break;case"users":this.setUsers(o)}return{success:!0,message:`${r.length} éléments importés avec succès`,errors:[]}}catch(a){return{success:!1,message:"Erreur lors de l'importation: "+a.message,errors:[a.message]}}}},Ha=Ua("Preferences",{web:()=>ba(()=>import("./web-6ea5MlRL.js"),__vite__mapDeps([0,1,2,3,4]),import.meta.url).then(e=>new e.PreferencesWeb)}),Ga=Object.freeze(Object.defineProperty({__proto__:null,Preferences:Ha},Symbol.toStringTag,{value:"Module"}));const Ya=new class{constructor(){n(this,"CSV_PREFIX","smartboutique_csv_")}async setCSV(e,t,n){try{const a=Oa.arrayToCSV(t,n);await Ha.set({key:this.CSV_PREFIX+e,value:a})}catch(a){throw console.error(`Erreur lors de la sauvegarde CSV ${e}:`,a),a}}async getCSV(e,t,n=[]){try{const a=await Ha.get({key:this.CSV_PREFIX+e});return a.value?Oa.csvToArray(a.value,t):n}catch(a){return console.error(`Erreur lors de la lecture CSV ${e}:`,a),n}}async getProducts(){return(await this.getCSV("products",Va,[])).map(e=>{if(!e.prixAchatCDF&&e.prixCDF&&(e.prixAchatCDF=.7*e.prixCDF),e.prixAchatCDF=Number(e.prixAchatCDF)||0,e.prixAchatUSD=e.prixAchatUSD?Number(e.prixAchatUSD):void 0,e.prixCDF=Number(e.prixCDF)||0,e.prixUSD=e.prixUSD?Number(e.prixUSD):void 0,!e.beneficeUnitaireCDF||isNaN(Number(e.beneficeUnitaireCDF))?e.beneficeUnitaireCDF=e.prixCDF-e.prixAchatCDF:e.beneficeUnitaireCDF=Number(e.beneficeUnitaireCDF),!e.beneficeUnitaireUSD||isNaN(Number(e.beneficeUnitaireUSD))){const t=2800;e.beneficeUnitaireUSD=Math.round(e.beneficeUnitaireCDF/t*100)/100}else e.beneficeUnitaireUSD=Number(e.beneficeUnitaireUSD);e.stock=Number(e.stock)||0,e.stockMin=Number(e.stockMin)||0;const t=(new Date).toISOString();return e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())||(e.dateCreation=t),e.dateModification&&!isNaN(new Date(e.dateModification).getTime())||(e.dateModification=t),e})}async setProducts(e){await this.setCSV("products",e,Va)}async getUsers(){return this.getCSV("users",qa,[])}async setUsers(e){await this.setCSV("users",e,qa)}async getSales(){return(await this.getCSV("sales",Ba,[])).map(e=>(e.totalCDF=Number(e.totalCDF)||0,e.totalUSD=e.totalUSD?Number(e.totalUSD):void 0,e.produits=(e.produits||[]).map(e=>({...e,quantite:Number(e.quantite)||0,prixUnitaireCDF:Number(e.prixUnitaireCDF)||0,prixUnitaireUSD:e.prixUnitaireUSD?Number(e.prixUnitaireUSD):void 0,totalCDF:Number(e.totalCDF)||0,totalUSD:e.totalUSD?Number(e.totalUSD):void 0})),e))}async setSales(e){await this.setCSV("sales",e,Ba)}async getDebts(){return(await this.getCSV("debts",_a,[])).map(e=>(e.statutPaiement||(e.statutPaiement="paid"===e.statut?"paye":"impaye"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantTotalUSD=e.montantTotalUSD?Number(e.montantTotalUSD):void 0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantPayeUSD=e.montantPayeUSD?Number(e.montantPayeUSD):void 0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,e.montantRestantUSD=e.montantRestantUSD?Number(e.montantRestantUSD):void 0,e.paiements=(e.paiements||[]).map(e=>({...e,montantCDF:Number(e.montantCDF)||0,montantUSD:e.montantUSD?Number(e.montantUSD):void 0})),e))}async setDebts(e){await this.setCSV("debts",e,_a)}async getExpenses(){return this.getCSV("expenses",$a,[])}async setExpenses(e){await this.setCSV("expenses",e,$a)}async getEmployees(){return this.getCSV("employees",za,[])}async setEmployees(e){await this.setCSV("employees",e,za)}async addEmployee(e){const t=await this.getEmployees();t.push(e),await this.setEmployees(t)}async updateEmployee(e){const t=await this.getEmployees(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,await this.setEmployees(t))}async deleteEmployee(e){const t=(await this.getEmployees()).filter(t=>t.id!==e);await this.setEmployees(t)}async getSettings(){const e=await Ha.get({key:this.CSV_PREFIX+"settings"});if(e.value)try{return JSON.parse(e.value)}catch(t){console.error("Erreur lors du parsing des paramètres:",t)}return{tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Électronique",description:"Appareils électroniques",couleur:"#2196F3"},{id:"2",nom:"Vêtements",description:"Vêtements et accessoires",couleur:"#4CAF50"},{id:"3",nom:"Alimentation",description:"Produits alimentaires",couleur:"#FF9800"},{id:"4",nom:"Maison",description:"Articles pour la maison",couleur:"#9C27B0"},{id:"5",nom:"Beauté",description:"Produits de beauté",couleur:"#E91E63"},{id:"6",nom:"Boissons",description:"Boissons et breuvages",couleur:"#00BCD4"},{id:"7",nom:"Épicerie",description:"Produits d'épicerie",couleur:"#795548"},{id:"8",nom:"Livres",description:"Livres et éducation",couleur:"#607D8B"},{id:"9",nom:"Sport",description:"Articles de sport",couleur:"#FF5722"},{id:"10",nom:"Santé",description:"Produits de santé",couleur:"#8BC34A"}],entreprise:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+*********** 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}}}async setSettings(e){await Ha.set({key:this.CSV_PREFIX+"settings",value:JSON.stringify(e)})}async getCurrentUser(){const e=await Ha.get({key:this.CSV_PREFIX+"currentUser"});return e.value?JSON.parse(e.value):null}async setCurrentUser(e){await Ha.set({key:this.CSV_PREFIX+"currentUser",value:JSON.stringify(e)})}async initializeDefaultData(){if(0===(await this.getUsers()).length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];await this.setUsers(e)}0===(await this.getProducts()).length&&await this.initializeProductCatalog();0===(await this.getDebts()).length&&await this.initializeSampleDebts()}async initializeProductCatalog(){const e=[{id:"1",nom:"iPhone 15",description:"Smartphone Apple iPhone 15 128GB",prixCDF:224e4,prixUSD:800,codeQR:"SB12345678ABCD",categorie:"Électronique",stock:25,stockMin:5,codeBarres:"1234567890123",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"2",nom:"T-shirt Nike",description:"T-shirt Nike en coton, taille M",prixCDF:98e3,prixUSD:35,codeQR:"SB12345679EFGH",categorie:"Vêtements",stock:50,stockMin:10,codeBarres:"1234567890124",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"3",nom:"Café Arabica",description:"Café Arabica premium 500g",prixCDF:33600,prixUSD:12,codeQR:"SB12345680IJKL",categorie:"Alimentation",stock:8,stockMin:15,codeBarres:"1234567890125",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"4",nom:"Sucre",description:"Sucre blanc cristallisé 1kg",prixCDF:8400,prixUSD:3,codeQR:"SB12345681MNOP",categorie:"Épicerie",stock:120,stockMin:20,codeBarres:"1234567890126",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"5",nom:"Riz",description:"Riz blanc parfumé 5kg",prixCDF:42e3,prixUSD:15,codeQR:"SB12345682QRST",categorie:"Alimentation",stock:80,stockMin:15,codeBarres:"1234567890127",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"6",nom:"Sel",description:"Sel de cuisine iodé 500g",prixCDF:2800,prixUSD:1,codeQR:"SB12345683UVWX",categorie:"Épicerie",stock:200,stockMin:30,codeBarres:"1234567890128",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"7",nom:"Lait",description:"Lait entier UHT 1 litre",prixCDF:5600,prixUSD:2,codeQR:"SB12345684YZAB",categorie:"Boissons",stock:60,stockMin:12,codeBarres:"1234567890129",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"8",nom:"Thé",description:"Thé noir en sachets, boîte de 25",prixCDF:11200,prixUSD:4,codeQR:"SB12345685CDEF",categorie:"Boissons",stock:45,stockMin:10,codeBarres:"1234567890130",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"9",nom:"Vestes",description:"Veste en jean unisexe, taille L",prixCDF:14e4,prixUSD:50,codeQR:"SB12345686GHIJ",categorie:"Vêtements",stock:30,stockMin:5,codeBarres:"1234567890131",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"10",nom:"Livres",description:'Roman français "Le Petit Prince"',prixCDF:22400,prixUSD:8,codeQR:"SB12345687KLMN",categorie:"Livres",stock:25,stockMin:5,codeBarres:"1234567890132",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"11",nom:"Huile de Palme",description:"Huile de palme rouge 1 litre",prixCDF:16800,prixUSD:6,codeQR:"SB12345688OPQR",categorie:"Alimentation",stock:40,stockMin:8,codeBarres:"1234567890133",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"12",nom:"Farine de Maïs",description:"Farine de maïs blanche 2kg",prixCDF:11200,prixUSD:4,codeQR:"SB12345689STUV",categorie:"Alimentation",stock:75,stockMin:15,codeBarres:"1234567890134",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()}];await this.setProducts(e)}async clearAllData(){try{const e=["products","users","sales","debts","expenses","settings","currentUser"];for(const t of e)await Ha.remove({key:this.CSV_PREFIX+t});console.log("✅ All CSV data cleared from mobile storage")}catch(e){throw console.error("❌ Error clearing CSV data:",e),e}}async initializeSampleDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()-12096e5),a=new Date(e.getTime()+2592e6),i=new Date(e.getTime()+12096e5),r=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:a.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 998 765 432",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:25e4,montantTotalUSD:89.29,montantPayeCDF:1e5,montantPayeUSD:35.71,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:n.toISOString(),dateEcheance:i.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:1e5,montantUSD:35.71,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e5).toISOString(),notes:"Paiement partiel en espèces"}],notes:"Paiement partiel effectué - Vêtements et accessoires"},{id:"DET-003",venteId:"VTE-CREDIT-003",nomClient:"Joseph Kabila Kabange",telephoneClient:"+243 811 222 333",adresseClient:"Avenue de la Paix, Lubumbashi",montantTotalCDF:18e4,montantTotalUSD:64.29,montantPayeCDF:18e4,montantPayeUSD:64.29,montantRestantCDF:0,montantRestantUSD:0,dateCreation:n.toISOString(),dateEcheance:a.toISOString(),statut:"paid",statutPaiement:"paye",paiements:[{id:"PAY-002",montantCDF:18e4,montantUSD:64.29,methodePaiement:"mobile_money",datePaiement:new Date(e.getTime()-1728e5).toISOString(),notes:"Paiement complet via Mobile Money"}],notes:"Dette entièrement payée - Alimentation et boissons"},{id:"DET-004",venteId:"VTE-CREDIT-004",nomClient:"Fatou Diallo Sankara",telephoneClient:"+243 977 888 999",adresseClient:"Quartier Matonge, Kinshasa",montantTotalCDF:32e4,montantTotalUSD:114.29,montantPayeCDF:8e4,montantPayeUSD:28.57,montantRestantCDF:24e4,montantRestantUSD:85.71,dateCreation:new Date(e.getTime()-3888e6).toISOString(),dateEcheance:new Date(e.getTime()-1296e6).toISOString(),statut:"overdue",statutPaiement:"impaye",paiements:[{id:"PAY-003",montantCDF:8e4,montantUSD:28.57,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e6).toISOString(),notes:"Paiement partiel initial"}],notes:"Dette en retard - Nécessite suivi"}];await this.setDebts(r)}};let Ka=null,Za=null,ei=null;const ti="undefined"==typeof window&&"undefined"!=typeof process&&(null==(e=process.versions)?void 0:e.electron);if(ti)try{Ka=require("better-sqlite3"),Za=require("electron").app,ei=require("path").join}catch(fr){console.warn("better-sqlite3 not available:",fr)}class ni{constructor(){if(n(this,"db"),n(this,"dbPath"),n(this,"isAvailable",!1),ti&&Ka)try{const e=(null==Za?void 0:Za.getPath("userData"))||"./data";this.dbPath=ei(e,"smartboutique.db"),this.initializeDatabase(),this.isAvailable=!0}catch(fr){console.error("Failed to initialize SQLite:",fr)}else console.warn("SQLite not available in this context")}initializeDatabase(){if(!Ka)throw new Error("better-sqlite3 not available");this.db=new Ka(this.dbPath),this.db.pragma("journal_mode = WAL"),this.db.pragma("synchronous = NORMAL"),this.db.pragma("cache_size = 1000"),this.db.pragma("temp_store = memory"),this.createTables(),this.createIndexes()}checkAvailability(){if(!this.isAvailable)throw new Error("SQLite storage not available in this context")}createTables(){this.db.exec("\n      CREATE TABLE IF NOT EXISTS products (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        description TEXT,\n        prixAchatCDF REAL NOT NULL,\n        prixAchatUSD REAL,\n        prixCDF REAL NOT NULL,\n        prixUSD REAL,\n        beneficeUnitaireCDF REAL,\n        beneficeUnitaireUSD REAL,\n        codeQR TEXT,\n        categorie TEXT,\n        stock INTEGER,\n        stockMin INTEGER,\n        codeBarres TEXT,\n        dateCreation TEXT,\n        dateModification TEXT,\n        quantiteEnStock INTEGER,\n        coutAchatStockCDF REAL,\n        coutAchatStockUSD REAL,\n        prixParPieceCDF REAL,\n        prixParPieceUSD REAL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS users (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        email TEXT NOT NULL UNIQUE,\n        role TEXT NOT NULL,\n        motDePasse TEXT NOT NULL,\n        dateCreation TEXT,\n        actif INTEGER DEFAULT 1\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS sales (\n        id TEXT PRIMARY KEY,\n        date TEXT NOT NULL,\n        client TEXT,\n        produits TEXT NOT NULL,\n        totalCDF REAL NOT NULL,\n        totalUSD REAL,\n        typePaiement TEXT NOT NULL,\n        typeVente TEXT NOT NULL,\n        vendeur TEXT NOT NULL,\n        numeroRecu TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS debts (\n        id TEXT PRIMARY KEY,\n        client TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        dateCreation TEXT NOT NULL,\n        dateEcheance TEXT,\n        statut TEXT NOT NULL,\n        description TEXT,\n        vendeur TEXT NOT NULL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS expenses (\n        id TEXT PRIMARY KEY,\n        description TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        date TEXT NOT NULL,\n        categorie TEXT NOT NULL,\n        utilisateur TEXT NOT NULL,\n        numeroRecu TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS settings (\n        key TEXT PRIMARY KEY,\n        value TEXT NOT NULL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS employees (\n        id TEXT PRIMARY KEY,\n        nomComplet TEXT NOT NULL,\n        poste TEXT NOT NULL,\n        salaireCDF REAL NOT NULL,\n        salaireUSD REAL,\n        dateEmbauche TEXT NOT NULL,\n        telephone TEXT,\n        adresse TEXT,\n        statut TEXT NOT NULL,\n        notes TEXT,\n        creePar TEXT NOT NULL,\n        dateCreation TEXT NOT NULL,\n        dateModification TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS employee_payments (\n        id TEXT PRIMARY KEY,\n        nomEmploye TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        datePaiement TEXT NOT NULL,\n        methodePaiement TEXT NOT NULL,\n        notes TEXT,\n        creePar TEXT NOT NULL,\n        dateCreation TEXT NOT NULL,\n        dateModification TEXT\n      )\n    ")}createIndexes(){this.db.exec("\n      CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);\n      CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);\n      CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);\n      CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);\n      CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);\n      CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);\n      CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);\n      CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_date ON employee_payments(datePaiement);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_employe ON employee_payments(nomEmploye);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_methode ON employee_payments(methodePaiement);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_cree_par ON employee_payments(creePar);\n    ")}getProducts(){this.checkAvailability();return this.db.prepare("SELECT * FROM products ORDER BY nom").all()}getProduct(e){this.checkAvailability();return this.db.prepare("SELECT * FROM products WHERE id = ?").get(e)}setProducts(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM products").run();const t=this.db.prepare("\n        INSERT INTO products (\n          id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n          beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n          stockMin, codeBarres, dateCreation, dateModification\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nom,n.description,n.prixAchatCDF,n.prixAchatUSD,n.prixCDF,n.prixUSD,n.beneficeUnitaireCDF,n.beneficeUnitaireUSD,n.codeQR,n.categorie,n.stock,n.stockMin,n.codeBarres,n.dateCreation,n.dateModification)})(e)}addProduct(e){this.db.prepare("\n      INSERT INTO products (\n        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n        stockMin, codeBarres, dateCreation, dateModification, quantiteEnStock,\n        coutAchatStockCDF, coutAchatStockUSD, prixParPieceCDF, prixParPieceUSD\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateCreation,e.dateModification,e.quantiteEnStock,e.coutAchatStockCDF,e.coutAchatStockUSD,e.prixParPieceCDF,e.prixParPieceUSD)}updateProduct(e){this.db.prepare("\n      UPDATE products SET\n        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,\n        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,\n        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,\n        dateModification = ?\n      WHERE id = ?\n    ").run(e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateModification,e.id)}deleteProduct(e){this.db.prepare("DELETE FROM products WHERE id = ?").run(e)}getUsers(){return this.db.prepare("SELECT * FROM users ORDER BY nom").all().map(e=>({...e,actif:Boolean(e.actif)}))}setUsers(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM users").run();const t=this.db.prepare("\n        INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nom,n.email,n.role,n.motDePasse,n.dateCreation,n.actif?1:0)})(e)}getSales(){return this.db.prepare("SELECT * FROM sales ORDER BY date DESC").all().map(e=>({...e,produits:JSON.parse(e.produits)}))}setSales(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM sales").run();const t=this.db.prepare("\n        INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu)\n        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.datevente,n.nomClient,JSON.stringify(n.produits),n.totalCDF,n.totalUSD,n.methodePaiement,n.typeVente,n.vendeur,n.numeroRecu)})(e)}getEmployeePayments(){this.checkAvailability();return this.db.prepare("SELECT * FROM employee_payments ORDER BY datePaiement DESC").all()}getEmployeePayment(e){this.checkAvailability();return this.db.prepare("SELECT * FROM employee_payments WHERE id = ?").get(e)}addEmployeePayment(e){this.checkAvailability();this.db.prepare("\n      INSERT INTO employee_payments (\n        id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,\n        notes, creePar, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nomEmploye,e.montantCDF,e.montantUSD,e.datePaiement,e.methodePaiement,e.notes,e.creePar,e.dateCreation,e.dateModification)}clearAllData(){this.checkAvailability();try{this.db.transaction(()=>{this.db.prepare("DELETE FROM products").run(),this.db.prepare("DELETE FROM users").run(),this.db.prepare("DELETE FROM sales").run(),this.db.prepare("DELETE FROM debts").run(),this.db.prepare("DELETE FROM expenses").run(),this.db.prepare("DELETE FROM employee_payments").run(),this.db.prepare("DELETE FROM settings").run(),this.db.prepare("DELETE FROM sqlite_sequence").run()})(),console.log("✅ All SQLite data cleared successfully")}catch(fr){throw console.error("❌ Error clearing SQLite data:",fr),fr}}updateEmployeePayment(e){this.checkAvailability();this.db.prepare("\n      UPDATE employee_payments SET\n        nomEmploye = ?, montantCDF = ?, montantUSD = ?, datePaiement = ?,\n        methodePaiement = ?, notes = ?, dateModification = ?\n      WHERE id = ?\n    ").run(e.nomEmploye,e.montantCDF,e.montantUSD,e.datePaiement,e.methodePaiement,e.notes,e.dateModification,e.id)}deleteEmployeePayment(e){this.checkAvailability();this.db.prepare("DELETE FROM employee_payments WHERE id = ?").run(e)}setEmployeePayments(e){this.checkAvailability();this.db.transaction(e=>{this.db.prepare("DELETE FROM employee_payments").run();const t=this.db.prepare("\n        INSERT INTO employee_payments (\n          id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,\n          notes, creePar, dateCreation, dateModification\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nomEmploye,n.montantCDF,n.montantUSD,n.datePaiement,n.methodePaiement,n.notes,n.creePar,n.dateCreation,n.dateModification)})(e)}migrateFromCSV(e){console.log("Starting migration from CSV to SQLite...");this.db.transaction(()=>{var t,n,a;(null==(t=e.products)?void 0:t.length)&&this.setProducts(e.products),(null==(n=e.users)?void 0:n.length)&&this.setUsers(e.users),(null==(a=e.sales)?void 0:a.length)&&this.setSales(e.sales)})(),console.log("Migration completed successfully")}exportToCSV(){return{products:"",users:"",sales:""}}close(){this.db.close()}getStats(){const e=this.db.prepare("SELECT COUNT(*) as count FROM products").get(),t=this.db.prepare("SELECT COUNT(*) as count FROM users").get(),n=this.db.prepare("SELECT COUNT(*) as count FROM sales").get(),a=this.db.prepare("SELECT COUNT(*) as count FROM employee_payments").get();return{products:e.count,users:t.count,sales:n.count,employeePayments:a.count,dbSize:0}}getEmployees(){this.checkAvailability();return this.db.prepare("SELECT * FROM employees ORDER BY nom ASC").all()}addEmployee(e){this.checkAvailability();this.db.prepare("\n      INSERT INTO employees (\n        id, nomComplet, poste, salaireCDF, salaireUSD, dateEmbauche,\n        telephone, adresse, statut, notes, creePar, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nomComplet,e.poste,e.salaireCDF,e.salaireUSD,e.dateEmbauche,e.telephone,e.adresse,e.statut,e.notes,e.creePar,e.dateCreation,e.dateModification)}updateEmployee(e){this.checkAvailability();this.db.prepare("\n      UPDATE employees SET\n        nomComplet = ?, poste = ?, salaireCDF = ?, salaireUSD = ?,\n        dateEmbauche = ?, telephone = ?, adresse = ?, statut = ?, notes = ?,\n        dateModification = ?\n      WHERE id = ?\n    ").run(e.nomComplet,e.poste,e.salaireCDF,e.salaireUSD,e.dateEmbauche,e.telephone,e.adresse,e.statut,e.notes,e.dateModification,e.id)}deleteEmployee(e){this.checkAvailability();this.db.prepare("DELETE FROM employees WHERE id = ?").run(e)}}const ai=new ni,ii=Object.freeze(Object.defineProperty({__proto__:null,SQLiteStorageService:ni,sqliteStorageService:ai},Symbol.toStringTag,{value:"Module"}));class ri{constructor(e){this.sqlite=e,this._connectionDict=new Map}async initWebStore(){try{return await this.sqlite.initWebStore(),Promise.resolve()}catch(e){return Promise.reject(e)}}async saveToStore(e){try{return await this.sqlite.saveToStore({database:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async saveToLocalDisk(e){try{return await this.sqlite.saveToLocalDisk({database:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async getFromLocalDiskToStore(e){const t=null==e||e;try{return await this.sqlite.getFromLocalDiskToStore({overwrite:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async echo(e){try{const t=await this.sqlite.echo({value:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isSecretStored(){try{const e=await this.sqlite.isSecretStored();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async setEncryptionSecret(e){try{return await this.sqlite.setEncryptionSecret({passphrase:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async changeEncryptionSecret(e,t){try{return await this.sqlite.changeEncryptionSecret({passphrase:e,oldpassphrase:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async clearEncryptionSecret(){try{return await this.sqlite.clearEncryptionSecret(),Promise.resolve()}catch(e){return Promise.reject(e)}}async checkEncryptionSecret(e){try{const t=await this.sqlite.checkEncryptionSecret({passphrase:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async addUpgradeStatement(e,t){try{return e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.addUpgradeStatement({database:e,upgrade:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async createConnection(e,t,n,a,i){try{e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.createConnection({database:e,encrypted:t,mode:n,version:a,readonly:i});const r=new si(e,i,this.sqlite),s=i?`RO_${e}`:`RW_${e}`;return this._connectionDict.set(s,r),Promise.resolve(r)}catch(r){return Promise.reject(r)}}async closeConnection(e,t){try{e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.closeConnection({database:e,readonly:t});const n=t?`RO_${e}`:`RW_${e}`;return this._connectionDict.delete(n),Promise.resolve()}catch(n){return Promise.reject(n)}}async isConnection(e,t){const n={};e.endsWith(".db")&&(e=e.slice(0,-3));const a=t?`RO_${e}`:`RW_${e}`;return n.result=this._connectionDict.has(a),Promise.resolve(n)}async retrieveConnection(e,t){e.endsWith(".db")&&(e=e.slice(0,-3));const n=t?`RO_${e}`:`RW_${e}`;if(this._connectionDict.has(n)){const t=this._connectionDict.get(n);return void 0!==t?Promise.resolve(t):Promise.reject(`Connection ${e} is undefined`)}return Promise.reject(`Connection ${e} does not exist`)}async getNCDatabasePath(e,t){try{const n=await this.sqlite.getNCDatabasePath({path:e,database:t});return Promise.resolve(n)}catch(n){return Promise.reject(n)}}async createNCConnection(e,t){try{await this.sqlite.createNCConnection({databasePath:e,version:t});const n=new si(e,!0,this.sqlite),a=`RO_${e})`;return this._connectionDict.set(a,n),Promise.resolve(n)}catch(n){return Promise.reject(n)}}async closeNCConnection(e){try{await this.sqlite.closeNCConnection({databasePath:e});const t=`RO_${e})`;return this._connectionDict.delete(t),Promise.resolve()}catch(t){return Promise.reject(t)}}async isNCConnection(e){const t={},n=`RO_${e})`;return t.result=this._connectionDict.has(n),Promise.resolve(t)}async retrieveNCConnection(e){if(this._connectionDict.has(e)){const t=`RO_${e})`,n=this._connectionDict.get(t);return void 0!==n?Promise.resolve(n):Promise.reject(`Connection ${e} is undefined`)}return Promise.reject(`Connection ${e} does not exist`)}async isNCDatabase(e){try{const t=await this.sqlite.isNCDatabase({databasePath:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async retrieveAllConnections(){return this._connectionDict}async closeAllConnections(){const e=new Map;try{for(const t of this._connectionDict.keys()){const n=t.substring(3),a="RO_"===t.substring(0,3);await this.sqlite.closeConnection({database:n,readonly:a}),e.set(t,null)}for(const t of e.keys())this._connectionDict.delete(t);return Promise.resolve()}catch(t){return Promise.reject(t)}}async checkConnectionsConsistency(){try{const e=[...this._connectionDict.keys()],t=[],n=[];for(const i of e)t.push(i.substring(0,2)),n.push(i.substring(3));const a=await this.sqlite.checkConnectionsConsistency({dbNames:n,openModes:t});return a.result||(this._connectionDict=new Map),Promise.resolve(a)}catch(e){return this._connectionDict=new Map,Promise.reject(e)}}async importFromJson(e){try{const t=await this.sqlite.importFromJson({jsonstring:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isJsonValid(e){try{const t=await this.sqlite.isJsonValid({jsonstring:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async copyFromAssets(e){const t=null==e||e;try{return await this.sqlite.copyFromAssets({overwrite:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async getFromHTTPRequest(e,t){const n=null==t||t;try{return await this.sqlite.getFromHTTPRequest({url:e,overwrite:n}),Promise.resolve()}catch(a){return Promise.reject(a)}}async isDatabaseEncrypted(e){e.endsWith(".db")&&(e=e.slice(0,-3));try{const t=await this.sqlite.isDatabaseEncrypted({database:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isInConfigEncryption(){try{const e=await this.sqlite.isInConfigEncryption();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isInConfigBiometricAuth(){try{const e=await this.sqlite.isInConfigBiometricAuth();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isDatabase(e){e.endsWith(".db")&&(e=e.slice(0,-3));try{const t=await this.sqlite.isDatabase({database:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async getDatabaseList(){try{const e=(await this.sqlite.getDatabaseList()).values;e.sort();const t={values:e};return Promise.resolve(t)}catch(e){return Promise.reject(e)}}async getMigratableDbList(e){const t=e||"default";try{const e=await this.sqlite.getMigratableDbList({folderPath:t});return Promise.resolve(e)}catch(n){return Promise.reject(n)}}async addSQLiteSuffix(e,t){const n=e||"default",a=t||[];try{const e=await this.sqlite.addSQLiteSuffix({folderPath:n,dbNameList:a});return Promise.resolve(e)}catch(i){return Promise.reject(i)}}async deleteOldDatabases(e,t){const n=e||"default",a=t||[];try{const e=await this.sqlite.deleteOldDatabases({folderPath:n,dbNameList:a});return Promise.resolve(e)}catch(i){return Promise.reject(i)}}async moveDatabasesAndAddSuffix(e,t){const n=e||"default",a=t||[];return this.sqlite.moveDatabasesAndAddSuffix({folderPath:n,dbNameList:a})}}class si{constructor(e,t,n){this.dbName=e,this.readonly=t,this.sqlite=n}getConnectionDBName(){return this.dbName}getConnectionReadOnly(){return this.readonly}async open(){try{return await this.sqlite.open({database:this.dbName,readonly:this.readonly}),Promise.resolve()}catch(e){return Promise.reject(e)}}async close(){try{return await this.sqlite.close({database:this.dbName,readonly:this.readonly}),Promise.resolve()}catch(e){return Promise.reject(e)}}async beginTransaction(){try{const e=await this.sqlite.beginTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async commitTransaction(){try{const e=await this.sqlite.commitTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async rollbackTransaction(){try{const e=await this.sqlite.rollbackTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isTransactionActive(){try{const e=await this.sqlite.isTransactionActive({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async loadExtension(e){try{return await this.sqlite.loadExtension({database:this.dbName,path:e,readonly:this.readonly}),Promise.resolve()}catch(t){return Promise.reject(t)}}async enableLoadExtension(e){try{return await this.sqlite.enableLoadExtension({database:this.dbName,toggle:e,readonly:this.readonly}),Promise.resolve()}catch(t){return Promise.reject(t)}}async getUrl(){try{const e=await this.sqlite.getUrl({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async getVersion(){try{const e=await this.sqlite.getVersion({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async getTableList(){try{const e=await this.sqlite.getTableList({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async execute(e,t=!0,n=!0){try{if(this.readonly)return Promise.reject("not allowed in read-only mode");{const a=await this.sqlite.execute({database:this.dbName,statements:e,transaction:t,readonly:!1,isSQL92:n});return Promise.resolve(a)}}catch(a){return Promise.reject(a)}}async query(e,t,n=!0){let a;try{return a=t&&t.length>0?await this.sqlite.query({database:this.dbName,statement:e,values:t,readonly:this.readonly,isSQL92:!0}):await this.sqlite.query({database:this.dbName,statement:e,values:[],readonly:this.readonly,isSQL92:n}),a=await this.reorderRows(a),Promise.resolve(a)}catch(i){return Promise.reject(i)}}async run(e,t,n=!0,a="no",i=!0){let r;try{return this.readonly?Promise.reject("not allowed in read-only mode"):(r=t&&t.length>0?await this.sqlite.run({database:this.dbName,statement:e,values:t,transaction:n,readonly:!1,returnMode:a,isSQL92:!0}):await this.sqlite.run({database:this.dbName,statement:e,values:[],transaction:n,readonly:!1,returnMode:a,isSQL92:i}),r.changes=await this.reorderRows(r.changes),Promise.resolve(r))}catch(s){return Promise.reject(s)}}async executeSet(e,t=!0,n="no",a=!0){let i;try{return this.readonly?Promise.reject("not allowed in read-only mode"):(i=await this.sqlite.executeSet({database:this.dbName,set:e,transaction:t,readonly:!1,returnMode:n,isSQL92:a}),i.changes=await this.reorderRows(i.changes),Promise.resolve(i))}catch(r){return Promise.reject(r)}}async isExists(){try{const e=await this.sqlite.isDBExists({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isTable(e){try{const t=await this.sqlite.isTableExists({database:this.dbName,table:e,readonly:this.readonly});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isDBOpen(){try{const e=await this.sqlite.isDBOpen({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async delete(){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.deleteDatabase({database:this.dbName,readonly:!1}),Promise.resolve())}catch(e){return Promise.reject(e)}}async createSyncTable(){try{if(this.readonly)return Promise.reject("not allowed in read-only mode");{const e=await this.sqlite.createSyncTable({database:this.dbName,readonly:!1});return Promise.resolve(e)}}catch(e){return Promise.reject(e)}}async setSyncDate(e){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.setSyncDate({database:this.dbName,syncdate:e,readonly:!1}),Promise.resolve())}catch(t){return Promise.reject(t)}}async getSyncDate(){try{const e=await this.sqlite.getSyncDate({database:this.dbName,readonly:this.readonly});let t="";return e.syncDate>0&&(t=new Date(1e3*e.syncDate).toISOString()),Promise.resolve(t)}catch(e){return Promise.reject(e)}}async exportToJson(e,t=!1){try{const n=await this.sqlite.exportToJson({database:this.dbName,jsonexportmode:e,readonly:this.readonly,encrypted:t});return Promise.resolve(n)}catch(n){return Promise.reject(n)}}async deleteExportedRows(){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.deleteExportedRows({database:this.dbName,readonly:!1}),Promise.resolve())}catch(e){return Promise.reject(e)}}async executeTransaction(e,t=!0){let n=0,a=!1;if(this.readonly)return Promise.reject("not allowed in read-only mode");if(await this.sqlite.beginTransaction({database:this.dbName}),a=await this.sqlite.isTransactionActive({database:this.dbName}),!a)return Promise.reject("After Begin Transaction, no transaction active");try{for(const i of e){if("object"!=typeof i||!("statement"in i))throw new Error("Error a task.statement must be provided");if("values"in i&&i.values&&i.values.length>0){const e=i.statement.toUpperCase().includes("RETURNING")?"all":"no",a=await this.sqlite.run({database:this.dbName,statement:i.statement,values:i.values,transaction:!1,readonly:!1,returnMode:e,isSQL92:t});if(a.changes.changes<0)throw new Error("Error in transaction method run ");n+=a.changes.changes}else{const e=await this.sqlite.execute({database:this.dbName,statements:i.statement,transaction:!1,readonly:!1});if(e.changes.changes<0)throw new Error("Error in transaction method execute ");n+=e.changes.changes}}n+=(await this.sqlite.commitTransaction({database:this.dbName})).changes.changes;const a={changes:{changes:n}};return Promise.resolve(a)}catch(i){const e=i.message?i.message:i;return await this.sqlite.rollbackTransaction({database:this.dbName}),Promise.reject(e)}}async reorderRows(e){const t=e;if((null==e?void 0:e.values)&&"object"==typeof e.values[0]&&Object.keys(e.values[0]).includes("ios_columns")){const n=e.values[0].ios_columns,a=[];for(let t=1;t<e.values.length;t++){const i=e.values[t],r={};for(const e of n)r[e]=i[e];a.push(r)}t.values=a}return Promise.resolve(t)}}const oi=Ua("CapacitorSQLite",{web:()=>ba(()=>import("./web-MyPlW46A.js"),__vite__mapDeps([5,1,2,3,4]),import.meta.url).then(e=>new e.CapacitorSQLiteWeb),electron:()=>window.CapacitorCustomPlatform.plugins.CapacitorSQLite});class li{constructor(){n(this,"sqlite"),n(this,"db",null),n(this,"DB_NAME","smartboutique.db"),n(this,"DB_VERSION",1),n(this,"isInitialized",!1),this.sqlite=new ri(oi)}async initialize(){if(!this.isInitialized)try{if(console.log("Initializing mobile SQLite database..."),!Fa.isNativePlatform())throw new Error("SQLite is only supported on native platforms (Android/iOS)");const e=await this.sqlite.checkConnectionsConsistency(),t=(await this.sqlite.isConnection(this.DB_NAME,!1)).result;e.result&&t?this.db=await this.sqlite.retrieveConnection(this.DB_NAME,!1):this.db=await this.sqlite.createConnection(this.DB_NAME,!1,"no-encryption",this.DB_VERSION,!1),await this.db.open(),await this.createTables(),await this.createIndexes(),this.isInitialized=!0,console.log("Mobile SQLite database initialized successfully")}catch(fr){throw console.error("Failed to initialize mobile SQLite database:",fr),fr}}async createTables(){if(!this.db)throw new Error("Database not initialized");const e=["CREATE TABLE IF NOT EXISTS products (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        description TEXT,\n        prixAchatCDF REAL NOT NULL,\n        prixAchatUSD REAL,\n        prixCDF REAL NOT NULL,\n        prixUSD REAL,\n        beneficeUnitaireCDF REAL,\n        beneficeUnitaireUSD REAL,\n        codeQR TEXT,\n        categorie TEXT,\n        stock INTEGER,\n        stockMin INTEGER,\n        codeBarres TEXT,\n        dateCreation TEXT,\n        dateModification TEXT,\n        quantiteEnStock INTEGER,\n        coutAchatStockCDF REAL,\n        coutAchatStockUSD REAL,\n        prixParPieceCDF REAL,\n        prixParPieceUSD REAL\n      );","CREATE TABLE IF NOT EXISTS users (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        email TEXT NOT NULL UNIQUE,\n        role TEXT NOT NULL,\n        motDePasse TEXT NOT NULL,\n        dateCreation TEXT,\n        actif INTEGER DEFAULT 1\n      );","CREATE TABLE IF NOT EXISTS sales (\n        id TEXT PRIMARY KEY,\n        date TEXT NOT NULL,\n        client TEXT,\n        produits TEXT NOT NULL,\n        totalCDF REAL NOT NULL,\n        totalUSD REAL,\n        typePaiement TEXT NOT NULL,\n        typeVente TEXT NOT NULL,\n        vendeur TEXT NOT NULL,\n        numeroRecu TEXT\n      );","CREATE TABLE IF NOT EXISTS debts (\n        id TEXT PRIMARY KEY,\n        client TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        dateCreation TEXT NOT NULL,\n        dateEcheance TEXT,\n        statut TEXT NOT NULL,\n        description TEXT,\n        vendeur TEXT NOT NULL\n      );","CREATE TABLE IF NOT EXISTS expenses (\n        id TEXT PRIMARY KEY,\n        description TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        date TEXT NOT NULL,\n        categorie TEXT NOT NULL,\n        utilisateur TEXT NOT NULL,\n        numeroRecu TEXT\n      );","CREATE TABLE IF NOT EXISTS settings (\n        key TEXT PRIMARY KEY,\n        value TEXT NOT NULL\n      );"];for(const t of e)await this.db.execute(t)}async createIndexes(){if(!this.db)throw new Error("Database not initialized");const e=["CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);","CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);","CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);","CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);","CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);","CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);","CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);","CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);"];for(const t of e)await this.db.execute(t)}async ensureInitialized(){this.isInitialized||await this.initialize()}async getProducts(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM products ORDER BY nom")).values}async getProduct(e){var t;if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return null==(t=(await this.db.query("SELECT * FROM products WHERE id = ?",[e])).values)?void 0:t[0]}async setProducts(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM products");for(const t of e)await this.db.run("INSERT INTO products (\n            id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n            beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n            stockMin, codeBarres, dateCreation, dateModification\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[t.id,t.nom,t.description,t.prixAchatCDF,t.prixAchatUSD,t.prixCDF,t.prixUSD,t.beneficeUnitaireCDF,t.beneficeUnitaireUSD,t.codeQR,t.categorie,t.stock,t.stockMin,t.codeBarres,t.dateCreation,t.dateModification]);await this.db.commitTransaction()}catch(fr){throw await this.db.rollbackTransaction(),fr}}async addProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("INSERT INTO products (\n        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n        stockMin, codeBarres, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[e.id,e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateCreation,e.dateModification])}async updateProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("UPDATE products SET\n        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,\n        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,\n        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,\n        dateModification = ?\n      WHERE id = ?",[e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateModification,e.id])}async deleteProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("DELETE FROM products WHERE id = ?",[e])}async getUsers(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM users ORDER BY nom")).values.map(e=>({...e,actif:Boolean(e.actif)}))}async setUsers(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM users");for(const t of e)await this.db.run("INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif) VALUES (?, ?, ?, ?, ?, ?, ?)",[t.id,t.nom,t.email,t.role,t.motDePasse,t.dateCreation,t.actif?1:0]);await this.db.commitTransaction()}catch(fr){throw await this.db.rollbackTransaction(),fr}}async getSales(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM sales ORDER BY date DESC")).values.map(e=>({...e,produits:JSON.parse(e.produits),datevente:e.date,nomClient:e.client,methodePaiement:e.typePaiement}))}async setSales(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM sales");for(const t of e)await this.db.run("INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[t.id,t.datevente,t.nomClient,JSON.stringify(t.produits),t.totalCDF,t.totalUSD,t.methodePaiement,t.typeVente,t.vendeur,t.numeroRecu]);await this.db.commitTransaction()}catch(fr){throw await this.db.rollbackTransaction(),fr}}async close(){this.db&&(await this.db.close(),await this.sqlite.closeConnection(this.DB_NAME,!1),this.db=null,this.isInitialized=!1)}async getStats(){var e,t,n,a,i,r;if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");const[s,o,l]=await Promise.all([this.db.query("SELECT COUNT(*) as count FROM products"),this.db.query("SELECT COUNT(*) as count FROM users"),this.db.query("SELECT COUNT(*) as count FROM sales")]);return{products:(null==(t=null==(e=s.values)?void 0:e[0])?void 0:t.count)||0,users:(null==(a=null==(n=o.values)?void 0:n[0])?void 0:a.count)||0,sales:(null==(r=null==(i=l.values)?void 0:i[0])?void 0:r.count)||0,dbSize:0}}async clearAllData(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");try{await this.db.execute("DELETE FROM products"),await this.db.execute("DELETE FROM users"),await this.db.execute("DELETE FROM sales"),await this.db.execute("DELETE FROM debts"),await this.db.execute("DELETE FROM expenses"),await this.db.execute("DELETE FROM settings"),await this.db.execute("DELETE FROM sqlite_sequence"),console.log("✅ All mobile SQLite data cleared successfully")}catch(fr){throw console.error("❌ Error clearing mobile SQLite data:",fr),fr}}}const ci=new li,di=Object.freeze(Object.defineProperty({__proto__:null,MobileSQLiteStorageService:li,mobileSQLiteStorageService:ci},Symbol.toStringTag,{value:"Module"}));const ui=new class{constructor(){n(this,"MIGRATION_FLAG_KEY","smartboutique_mobile_migrated_to_sqlite")}async isMigrationCompleted(){return"true"===(await Ha.get({key:this.MIGRATION_FLAG_KEY})).value}async markMigrationCompleted(){await Ha.set({key:this.MIGRATION_FLAG_KEY,value:"true"})}async migrateToSQLite(){const e={success:!1,message:"",migratedCounts:{products:0,users:0,sales:0,debts:0,expenses:0},errors:[]};try{if(console.log("Starting mobile migration from CSV to SQLite..."),await this.isMigrationCompleted())return e.success=!0,e.message="Migration mobile déjà effectuée",e;await ci.initialize();const t=await this.extractCSVData();if(!this.validateCSVData(t))return e.errors.push("Données CSV mobiles invalides ou corrompues"),e.message="Échec de la validation des données CSV mobiles",e;await this.createBackup(t),await this.performMigration(t,e),await this.verifyMigration(t,e)?(await this.markMigrationCompleted(),e.success=!0,e.message=`Migration mobile réussie: ${e.migratedCounts.products} produits, ${e.migratedCounts.users} utilisateurs, ${e.migratedCounts.sales} ventes migrées`):e.message="Échec de la vérification de la migration mobile"}catch(fr){console.error("Mobile migration error:",fr),e.errors.push(`Erreur de migration mobile: ${fr.message}`),e.message="Échec de la migration mobile"}return e}async extractCSVData(){return console.log("Extracting data from CSV Capacitor Preferences..."),{products:await Ya.getProducts()||[],users:await Ya.getUsers()||[],sales:await Ya.getSales()||[],debts:await Ya.getDebts()||[],expenses:await Ya.getExpenses()||[]}}validateCSVData(e){try{if(!e||"object"!=typeof e)return!1;const{products:t,users:n,sales:a,debts:i,expenses:r}=e;if(t&&Array.isArray(t))for(const e of t)if(!e.id||!e.nom||"number"!=typeof e.prixCDF)return console.warn("Invalid mobile product found:",e),!1;if(n&&Array.isArray(n))for(const e of n)if(!(e.id&&e.nom&&e.email&&e.role))return console.warn("Invalid mobile user found:",e),!1;if(a&&Array.isArray(a))for(const e of a)if(!e.id||!e.datevente||!Array.isArray(e.produits))return console.warn("Invalid mobile sale found:",e),!1;return!0}catch(fr){return console.error("Mobile data validation error:",fr),!1}}async createBackup(e){try{const t={timestamp:(new Date).toISOString(),platform:"mobile",data:e};await Ha.set({key:"smartboutique_mobile_csv_backup",value:JSON.stringify(t)}),console.log("Mobile backup created successfully")}catch(fr){throw console.error("Mobile backup creation failed:",fr),new Error("Impossible de créer une sauvegarde mobile")}}async performMigration(e,t){try{const{products:n,users:a,sales:i,debts:r,expenses:s}=e;n&&n.length>0&&(await ci.setProducts(n),t.migratedCounts.products=n.length,console.log(`Migrated ${n.length} mobile products`)),a&&a.length>0&&(await ci.setUsers(a),t.migratedCounts.users=a.length,console.log(`Migrated ${a.length} mobile users`)),i&&i.length>0&&(await ci.setSales(i),t.migratedCounts.sales=i.length,console.log(`Migrated ${i.length} mobile sales`)),console.log("Mobile migration to SQLite completed")}catch(fr){throw console.error("Mobile migration execution failed:",fr),new Error(`Échec de la migration mobile: ${fr.message}`)}}async verifyMigration(e,t){try{const n=await ci.getStats();return e.products&&e.products.length!==n.products?(t.errors.push(`Nombre de produits mobile incorrect: attendu ${e.products.length}, trouvé ${n.products}`),!1):e.users&&e.users.length!==n.users?(t.errors.push(`Nombre d'utilisateurs mobile incorrect: attendu ${e.users.length}, trouvé ${n.users}`),!1):e.sales&&e.sales.length!==n.sales?(t.errors.push(`Nombre de ventes mobile incorrect: attendu ${e.sales.length}, trouvé ${n.sales}`),!1):(console.log("Mobile migration verification successful"),!0)}catch(fr){return console.error("Mobile migration verification failed:",fr),t.errors.push(`Échec de la vérification mobile: ${fr.message}`),!1}}async rollbackMigration(){try{console.log("Rolling back mobile migration...");const e=await Ha.get({key:"smartboutique_mobile_csv_backup"});if(!e.value)return console.error("No mobile backup found for rollback"),!1;const t=JSON.parse(e.value),{data:n}=t;return n.products&&await Ya.setProducts(n.products),n.users&&await Ya.setUsers(n.users),n.sales&&await Ya.setSales(n.sales),n.debts&&await Ya.setDebts(n.debts),n.expenses&&await Ya.setExpenses(n.expenses),await Ha.remove({key:this.MIGRATION_FLAG_KEY}),await ci.close(),console.log("Mobile migration rollback completed"),!0}catch(fr){return console.error("Mobile rollback failed:",fr),!1}}async getMigrationStatus(){const e=await this.isMigrationCompleted(),t=await Ha.get({key:"smartboutique_mobile_csv_backup"}),n=await Ha.get({key:"smartboutique_csv_products"});return{isCompleted:e,sqliteStats:e?await ci.getStats():void 0,csvDataExists:Boolean(n.value),backupExists:Boolean(t.value)}}async cleanupOldData(){if(!(await this.isMigrationCompleted()))return void console.warn("Cannot cleanup mobile data: migration not completed");const e=["smartboutique_csv_products","smartboutique_csv_users","smartboutique_csv_sales","smartboutique_csv_debts","smartboutique_csv_expenses","smartboutique_csv_settings"];for(const t of e)await Ha.remove({key:t});console.log("Old mobile CSV data cleaned up")}async forceMigration(){return await Ha.remove({key:this.MIGRATION_FLAG_KEY}),this.migrateToSQLite()}};const mi=new class{constructor(){n(this,"migrationChecked",!1),n(this,"mobileMigrationChecked",!1)}get storage(){return La()?Ya:Ja}get sqliteStorage(){return La()?ci:ai}async checkDesktopMigration(){if(!this.migrationChecked&&!La()){this.migrationChecked=!0;try{return void console.log("SQLite migration temporarily disabled - using CSV storage")}catch(fr){console.error("Desktop migration check failed:",fr)}}}async checkMobileMigration(){if(!this.mobileMigrationChecked&&La()){this.mobileMigrationChecked=!0;try{if(!(await ui.isMigrationCompleted())){console.log("Starting automatic mobile migration to SQLite...");const e=await ui.migrateToSQLite();e.success?console.log("Mobile migration completed successfully:",e.message):(console.error("Mobile migration failed:",e.message,e.errors),console.log("Falling back to CSV storage on mobile"))}}catch(fr){console.error("Mobile migration check failed:",fr),console.log("Falling back to CSV storage on mobile due to error")}}}async checkMigration(){La()?await this.checkMobileMigration():await this.checkDesktopMigration()}async set(e,t){La()?console.warn("Generic set method not available in CSV storage"):Ja.set(e,t)}async get(e,t){return La()?(console.warn("Generic get method not available in CSV storage"),t):Ja.get(e,t)}async remove(e){La()?console.warn("Generic remove method not available in CSV storage"):Ja.remove(e)}async clear(){La()?await this.clearMobileData():await this.clearDesktopData()}async clearMobileData(){try{await Ya.clearAllData();const{Preferences:e}=await ba(async()=>{const{Preferences:e}=await Promise.resolve().then(()=>Ga);return{Preferences:e}},void 0,import.meta.url),{keys:t}=await e.keys(),n=t.filter(e=>e.startsWith("smartboutique_")||e.startsWith("smartboutique_csv_"));for(const a of n)await e.remove({key:a});try{await ui.isMigrationCompleted()&&await this.sqliteStorage.clearAllData()}catch(fr){console.warn("Mobile SQLite clear failed or not available:",fr)}console.log("✅ Mobile data cleared successfully")}catch(fr){throw console.error("❌ Error clearing mobile data:",fr),fr}}async clearDesktopData(){try{Ja.clear();try{ai.clearAllData()}catch(fr){console.warn("Desktop SQLite clear failed or not available:",fr)}console.log("✅ Desktop data cleared successfully")}catch(fr){throw console.error("❌ Error clearing desktop data:",fr),fr}}async getUsers(){if(await this.checkMigration(),La()){try{if(await ui.isMigrationCompleted())return await this.sqliteStorage.getUsers()}catch(fr){console.warn("Mobile SQLite failed, falling back to CSV:",fr)}return await Ya.getUsers()}return Ja.getUsers()}async setUsers(e){if(await this.checkMigration(),La()){try{if(await ui.isMigrationCompleted())return void(await this.sqliteStorage.setUsers(e))}catch(fr){console.warn("Mobile SQLite failed, falling back to CSV:",fr)}await Ya.setUsers(e)}else Ja.setUsers(e)}async getProducts(){if(await this.checkMigration(),La()){try{if(await ui.isMigrationCompleted())return await this.sqliteStorage.getProducts()}catch(fr){console.warn("Mobile SQLite failed, falling back to CSV:",fr)}return await Ya.getProducts()}return Ja.getProducts()}async setProducts(e){if(await this.checkMigration(),La()){try{if(await ui.isMigrationCompleted())return void(await this.sqliteStorage.setProducts(e))}catch(fr){console.warn("Mobile SQLite failed, falling back to CSV:",fr)}await Ya.setProducts(e)}else Ja.setProducts(e)}async getSales(){if(await this.checkMigration(),La()){try{if(await ui.isMigrationCompleted())return await this.sqliteStorage.getSales()}catch(fr){console.warn("Mobile SQLite failed, falling back to CSV:",fr)}return await Ya.getSales()}return Ja.getSales()}async setSales(e){if(await this.checkMigration(),La()){try{if(await ui.isMigrationCompleted())return void(await this.sqliteStorage.setSales(e))}catch(fr){console.warn("Mobile SQLite failed, falling back to CSV:",fr)}await Ya.setSales(e)}else Ja.setSales(e)}async getDebts(){return La()?await Ya.getDebts():Ja.getDebts()}async setDebts(e){La()?await Ya.setDebts(e):Ja.setDebts(e)}async getDettes(){return this.getDebts()}async setDettes(e){await this.setDebts(e)}async getCreances(){return this.getDebts()}async setCreances(e){await this.setDebts(e)}async getExpenses(){return La()?await Ya.getExpenses():Ja.getExpenses()}async setExpenses(e){La()?await Ya.setExpenses(e):Ja.setExpenses(e)}async getEmployeePayments(){var e,t;if(La())return await(null==(e=Ya.getEmployeePayments)?void 0:e.call(Ya))||[];try{return ai.getEmployeePayments()}catch(fr){return console.warn("SQLite not available for employee payments, using localStorage:",fr),(null==(t=Ja.getEmployeePayments)?void 0:t.call(Ja))||[]}}async addEmployeePayment(e){if(La())Ya.addEmployeePayment&&await Ya.addEmployeePayment(e);else try{ai.addEmployeePayment(e)}catch(fr){console.warn("SQLite not available for employee payments, using localStorage:",fr),Ja.addEmployeePayment&&Ja.addEmployeePayment(e)}}async updateEmployeePayment(e){if(La())Ya.updateEmployeePayment&&await Ya.updateEmployeePayment(e);else try{ai.updateEmployeePayment(e)}catch(fr){console.warn("SQLite not available for employee payments, using localStorage:",fr),Ja.updateEmployeePayment&&Ja.updateEmployeePayment(e)}}async deleteEmployeePayment(e){if(La())Ya.deleteEmployeePayment&&await Ya.deleteEmployeePayment(e);else try{ai.deleteEmployeePayment(e)}catch(fr){console.warn("SQLite not available for employee payments, using localStorage:",fr),Ja.deleteEmployeePayment&&Ja.deleteEmployeePayment(e)}}async setEmployeePayments(e){if(La())Ya.setEmployeePayments&&await Ya.setEmployeePayments(e);else try{ai.setEmployeePayments(e)}catch(fr){console.warn("SQLite not available for employee payments, using localStorage:",fr),Ja.setEmployeePayments&&Ja.setEmployeePayments(e)}}async getSettings(){return La()?await Ya.getSettings():Ja.getSettings()}async setSettings(e){La()?await Ya.setSettings(e):Ja.setSettings(e)}async getCurrentUser(){return La()?await Ya.getCurrentUser():Ja.getCurrentUser()}async setCurrentUser(e){La()?await Ya.setCurrentUser(e):Ja.setCurrentUser(e)}async initializeDefaultData(){console.log("🔄 Initializing fresh demo data..."),La()?await Ya.initializeDefaultData():Ja.initializeDefaultData();0===(await this.getDebts()).length&&await this.forceInitializeDebts();0===(await this.getSales()).length&&await this.initializeSampleSales();0===(await this.getExpenses()).length&&await this.initializeSampleExpenses(),console.log("✅ Fresh demo data initialized successfully")}async initializeSampleSales(){const e=new Date,t=new Date(e.getTime()-864e5),n=new Date(e.getTime()-1728e5),a=[{id:"1",numeroRecu:"RV-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0001",datevente:t.toISOString(),nomClient:"Marie Kabila",telephoneClient:"+243 900 000 001",produits:[{produitId:"1",nomProduit:"iPhone 15",quantite:1,prixUnitaireCDF:224e4,prixUnitaireUSD:800,totalCDF:224e4,totalUSD:800}],totalCDF:224e4,totalUSD:800,methodePaiement:"cash",typeVente:"cash",vendeur:"Super Admin",notes:"Vente comptant - Client satisfait"},{id:"2",numeroRecu:"RV-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0002",datevente:n.toISOString(),nomClient:"Jean Mukendi",telephoneClient:"+243 900 000 002",produits:[{produitId:"2",nomProduit:"T-shirt Nike",quantite:2,prixUnitaireCDF:98e3,prixUnitaireUSD:35,totalCDF:196e3,totalUSD:70}],totalCDF:196e3,totalUSD:70,methodePaiement:"mobile_money",typeVente:"cash",vendeur:"Gestionnaire",notes:"Paiement Mobile Money - Airtel"}];await this.setSales(a),console.log("✅ Sample sales data initialized")}async initializeSampleExpenses(){const e=new Date,t=new Date(e.getTime()-6048e5),n=[{id:"1",numeroRecu:"EX-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0001",dateDepense:t.toISOString(),description:"Achat de fournitures de bureau",montantCDF:28e4,montantUSD:100,categorie:"Fournitures",methodePaiement:"cash",creePar:"Super Admin",notes:"Papier, stylos, et autres fournitures",dateCreation:t.toISOString(),dateModification:t.toISOString()},{id:"2",numeroRecu:"EX-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0002",dateDepense:e.toISOString(),description:"Frais de transport - Livraison",montantCDF:14e4,montantUSD:50,categorie:"Transport",methodePaiement:"mobile_money",creePar:"Gestionnaire",notes:"Transport pour livraison clients",dateCreation:e.toISOString(),dateModification:e.toISOString()}];await this.setExpenses(n),console.log("✅ Sample expenses data initialized")}async forceInitializeDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()+2592e6),a=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:n.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 823 456 789",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:75e3,montantTotalUSD:26.79,montantPayeCDF:25e3,montantPayeUSD:8.93,montantRestantCDF:5e4,montantRestantUSD:17.86,dateCreation:t.toISOString(),dateEcheance:n.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:25e3,montantUSD:8.93,methodePaiement:"cash",datePaiement:e.toISOString(),notes:"Paiement partiel"}],notes:"Dette partiellement payée"}];await this.setDebts(a)}async exportData(){if(La()){const{csvImportExportService:e}=await ba(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>hr);return{csvImportExportService:e}},void 0,import.meta.url),t=await e.exportAllData();return t.data?{csvData:t.data,exportDate:(new Date).toISOString()}:{}}return Ja.exportData()}async importData(e){if(La()){if(e.csvData&&"string"==typeof e.csvData){const{csvImportExportService:e}=await ba(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>hr);return{csvImportExportService:e}},void 0,import.meta.url);return console.log("CSV import for mobile needs full implementation"),!1}return Ja.importData(e)}return Ja.importData(e)}async migrateFromDesktop(){if(!La())return console.warn("Migration should only be called on mobile platform"),!1;try{if((await Ya.getUsers()).length>0)return console.log("CSV storage already has data, skipping migration"),!0;const e={users:localStorage.getItem("smartboutique_users"),products:localStorage.getItem("smartboutique_products"),sales:localStorage.getItem("smartboutique_sales"),debts:localStorage.getItem("smartboutique_debts"),expenses:localStorage.getItem("smartboutique_expenses"),settings:localStorage.getItem("smartboutique_settings"),currentUser:localStorage.getItem("smartboutique_currentUser")};let t=!1;return e.users&&(await Ya.setUsers(JSON.parse(e.users)),t=!0),e.products&&(await Ya.setProducts(JSON.parse(e.products)),t=!0),e.sales&&(await Ya.setSales(JSON.parse(e.sales)),t=!0),e.debts&&(await Ya.setDebts(JSON.parse(e.debts)),t=!0),e.expenses&&(await Ya.setExpenses(JSON.parse(e.expenses)),t=!0),e.settings&&(await Ya.setSettings(JSON.parse(e.settings)),t=!0),e.currentUser&&(await Ya.setCurrentUser(JSON.parse(e.currentUser)),t=!0),t?console.log("Successfully migrated data from desktop to mobile"):console.log("No desktop data found to migrate"),!0}catch(fr){return console.error("Error during migration:",fr),!1}}async getEmployees(){await this.checkMigration();let e=[];if(La()){try{await ui.isMigrationCompleted()&&(e=await this.sqliteStorage.getEmployees())}catch(fr){console.warn("Mobile SQLite failed for employees, falling back to CSV:",fr)}0===e.length&&(e=Ya.getEmployees?await Ya.getEmployees():[])}else try{e=ai.getEmployees()}catch(fr){console.warn("SQLite not available for employees, falling back to localStorage storage:",fr),e=Ja.getEmployees?Ja.getEmployees():[]}return this.migrateEmployeeStructure(e)}migrateEmployeeStructure(e){return e.map(e=>{if(e.nom&&!e.nomComplet){const t=e.prenom?`${e.prenom} ${e.nom}`:e.nom;return{...e,nomComplet:t,nom:void 0,prenom:void 0}}return e})}async addEmployee(e){if(await this.checkMigration(),La()){try{if(await ui.isMigrationCompleted())return void(await this.sqliteStorage.addEmployee(e))}catch(fr){console.warn("Mobile SQLite failed for employees, falling back to CSV:",fr)}if(!Ya.addEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Ya.addEmployee(e)}else try{ai.addEmployee(e)}catch(fr){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",fr),!Ja.addEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");Ja.addEmployee(e)}}async updateEmployee(e){if(await this.checkMigration(),La()){try{if(await ui.isMigrationCompleted())return void(await this.sqliteStorage.updateEmployee(e))}catch(fr){console.warn("Mobile SQLite failed for employees, falling back to CSV:",fr)}if(!Ya.updateEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Ya.updateEmployee(e)}else try{ai.updateEmployee(e)}catch(fr){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",fr),!Ja.updateEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");Ja.updateEmployee(e)}}async deleteEmployee(e){if(await this.checkMigration(),La()){try{if(await ui.isMigrationCompleted())return void(await this.sqliteStorage.deleteEmployee(e))}catch(fr){console.warn("Mobile SQLite failed for employees, falling back to CSV:",fr)}if(!Ya.deleteEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Ya.deleteEmployee(e)}else try{ai.deleteEmployee(e)}catch(fr){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",fr),!Ja.deleteEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");Ja.deleteEmployee(e)}}};const hi=new class{constructor(){n(this,"currentUser",null),n(this,"initialized",!1)}async initialize(){this.initialized||(this.currentUser=await mi.getCurrentUser(),this.initialized=!0)}async login(e,t){await this.initialize();const n=(await mi.getUsers()).find(n=>n.email===e&&n.motDePasse===t&&n.actif);return n?(this.currentUser=n,await mi.setCurrentUser(n),{success:!0,user:n}):{success:!1,message:"Email ou mot de passe incorrect"}}async logout(){this.currentUser=null,await mi.remove("currentUser")}getCurrentUser(){return this.currentUser}async getCurrentUserAsync(){return await this.initialize(),this.currentUser}isAuthenticated(){return null!==this.currentUser}getUserPermissions(e){var t;switch(e||(null==(t=this.currentUser)?void 0:t.role)){case"super_admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!0,canViewSettings:!0,canManageSettings:!0,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!0,canViewEmployeePayments:!0,canManageEmployeePayments:!0};case"admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!1,canViewSettings:!0,canManageSettings:!1,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!1,canViewEmployeePayments:!0,canManageEmployeePayments:!0};case"employee":return{canViewDashboard:!1,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1,canViewEmployeePayments:!1,canManageEmployeePayments:!1};default:return{canViewDashboard:!1,canViewProducts:!1,canManageProducts:!1,canViewSales:!1,canManageSales:!1,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1,canViewEmployeePayments:!1,canManageEmployeePayments:!1}}}hasPermission(e){if(!this.currentUser)return!1;return this.getUserPermissions()[e]}hasRole(e){return!!this.currentUser&&e.includes(this.currentUser.role)}canAccessRoute(e){if(!this.currentUser)return!1;const t=this.getUserPermissions();switch(e){case"/dashboard":return t.canViewDashboard;case"/products":return t.canViewProducts;case"/sales":return t.canViewSales;case"/debts":return t.canViewDebts;case"/reports":return t.canViewReports;case"/users":return t.canViewUsers;case"/settings":return t.canViewSettings;case"/expenses":return t.canViewExpenses;case"/employee-payments":return t.canViewEmployeePayments;default:return!0}}getNavigationItems(){if(!this.currentUser)return[];const e=this.getUserPermissions(),t=[];return e.canViewDashboard&&t.push({label:"Tableau de bord",path:"/dashboard",icon:"Dashboard"}),e.canViewProducts&&t.push({label:"Inventaire",path:"/products",icon:"Inventory"}),e.canViewSales&&t.push({label:"Ventes",path:"/sales",icon:"PointOfSale"}),e.canViewDebts&&t.push({label:"Dettes",path:"/debts",icon:"AccountBalance"}),e.canViewExpenses&&t.push({label:"Dépenses",path:"/expenses",icon:"Receipt"}),e.canViewEmployeePayments&&t.push({label:"Paiements Employés",path:"/employee-payments",icon:"Payment"}),e.canViewReports&&t.push({label:"Rapports",path:"/reports",icon:"Assessment"}),e.canViewUsers&&t.push({label:"Utilisateurs",path:"/users",icon:"People"}),e.canViewSettings&&t.push({label:"Paramètres",path:"/settings",icon:"Settings"}),t}async updateProfile(e){if(!this.currentUser)return{success:!1,message:"Utilisateur non connecté"};try{const t=await mi.getUsers(),n=t.findIndex(e=>e.id===this.currentUser.id);if(-1===n)return{success:!1,message:"Utilisateur non trouvé"};const a={...t[n],...e};return t[n]=a,await mi.setUsers(t),this.currentUser=a,await mi.setCurrentUser(a),{success:!0}}catch(fr){return{success:!1,message:"Erreur lors de la mise à jour du profil"}}}async changePassword(e,t){return this.currentUser?this.currentUser.motDePasse!==e?{success:!1,message:"Mot de passe actuel incorrect"}:t.length<6?{success:!1,message:"Le nouveau mot de passe doit contenir au moins 6 caractères"}:this.updateProfile({motDePasse:t}):{success:!1,message:"Utilisateur non connecté"}}},pi=({currentUser:e,onLogout:t})=>{var n,F;const[U,T]=Ct.useState(!1),k=Kn(),R=Gn(),M=e?hi.getUserPermissions():null,A=[{label:"Inventaire",path:"/products",icon:a.jsx(D,{}),permission:"canViewProducts"},{label:"Ventes",path:"/sales",icon:a.jsx(S,{}),permission:"canViewSales"},{label:"Dettes",path:"/debts",icon:a.jsx(v,{}),permission:"canViewDebts"},{label:"Plus",path:"/more",icon:a.jsx(C,{}),permission:null}],I=[{label:"Tableau de bord",path:"/dashboard",icon:a.jsx(b,{}),permission:"canViewDashboard"},{label:"Dépenses",path:"/expenses",icon:a.jsx(f,{}),permission:"canViewExpenses"},{label:"Rapports",path:"/reports",icon:a.jsx(w,{}),permission:"canViewReports"},{label:"Utilisateurs",path:"/users",icon:a.jsx(E,{}),permission:"canViewUsers"},{label:"Paramètres",path:"/settings",icon:a.jsx(P,{}),permission:"canViewSettings"}],N=e=>!e||!M||M[e];return a.jsxs(i,{sx:{display:"flex",flexDirection:"column",height:"100vh"},children:[a.jsx(r,{position:"fixed",sx:{zIndex:e=>e.zIndex.drawer+1},children:a.jsxs(s,{children:[a.jsx(o,{variant:"h6",component:"div",sx:{flexGrow:1},children:"SmartBoutique"}),a.jsx(l,{sx:{bgcolor:"secondary.main",width:32,height:32},children:(null==(n=null==e?void 0:e.nom)?void 0:n.charAt(0))||"U"})]})}),a.jsx(i,{component:"main",sx:{flexGrow:1,pt:8,pb:7,overflow:"auto"},children:a.jsx(da,{})}),a.jsx(c,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:3,children:a.jsx(d,{value:(()=>{const e=R.pathname;return A.find(t=>t.path===e)?e:"/more"})(),onChange:(e,t)=>{var n;"/more"===(n=t)?T(!0):k(n)},showLabels:!0,children:A.map(e=>N(e.permission)&&a.jsx(u,{label:e.label,value:e.path,icon:e.icon},e.path))})}),a.jsxs(m,{anchor:"right",open:U,onClose:()=>T(!1),PaperProps:{sx:{width:280}},children:[a.jsx(s,{}),a.jsx(i,{sx:{p:2},children:a.jsxs(i,{sx:{display:"flex",alignItems:"center",mb:2},children:[a.jsx(l,{sx:{bgcolor:"primary.main",mr:2},children:(null==(F=null==e?void 0:e.nom)?void 0:F.charAt(0))||"U"}),a.jsxs(i,{children:[a.jsx(o,{variant:"subtitle1",fontWeight:"bold",children:(null==e?void 0:e.nom)||"Utilisateur"}),a.jsx(o,{variant:"body2",color:"text.secondary",children:(null==e?void 0:e.role)||"Rôle"})]})]})}),a.jsx(h,{}),a.jsxs(p,{children:[I.map(e=>N(e.permission)&&a.jsxs(x,{onClick:()=>{return t=e.path,k(t),void T(!1);var t},sx:{cursor:"pointer"},children:[a.jsx(g,{children:e.icon}),a.jsx(y,{primary:e.label})]},e.path)),a.jsx(h,{sx:{my:1}}),a.jsxs(x,{onClick:()=>{t(),T(!1)},sx:{cursor:"pointer"},children:[a.jsx(g,{children:a.jsx(j,{})}),a.jsx(y,{primary:"Déconnexion"})]})]})]})]})},xi={lessThanXSeconds:{one:"moins d’une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d’une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d’un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu’un an",other:"presque {{count}} ans"}},gi={date:wt({formats:{full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},defaultWidth:"full"}),time:wt({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:wt({formats:{full:"{{date}} 'à' {{time}}",long:"{{date}} 'à' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},yi={lastWeek:"eeee 'dernier à' p",yesterday:"'hier à' p",today:"'aujourd’hui à' p",tomorrow:"'demain à' p'",nextWeek:"eeee 'prochain à' p",other:"P"},ji=["MMM","MMMM"],Di={code:"fr",formatDistance:(e,t,n)=>{let a;const i=xi[e];return a="string"==typeof i?i:1===t?i.one:i.other.replace("{{count}}",String(t)),(null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"dans "+a:"il y a "+a:a},formatLong:gi,formatRelative:(e,t,n,a)=>yi[e],localize:{preprocessor:(e,t)=>{if(1===e.getDate())return t;return t.some(e=>e.isToken&&ji.includes(e.value))?t.map(e=>e.isToken&&"do"===e.value?{isToken:!0,value:"d"}:e):t},ordinalNumber:(e,t)=>{const n=Number(e),a=null==t?void 0:t.unit;if(0===n)return"0";let i;return i=1===n?a&&["year","week","hour","minute","second"].includes(a)?"ère":"er":"ème",n+i},era:Et({values:{narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant Jésus-Christ","après Jésus-Christ"]},defaultWidth:"wide"}),quarter:Et({values:{narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2ème trim.","3ème trim.","4ème trim."],wide:["1er trimestre","2ème trimestre","3ème trimestre","4ème trimestre"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:Et({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],wide:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"]},defaultWidth:"wide"}),day:Et({values:{narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},defaultWidth:"wide"}),dayPeriod:Et({values:{narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"après-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l’après-midi",evening:"du soir",night:"du matin"}},defaultWidth:"wide"})},match:{ordinalNumber:Ft({matchPattern:/^(\d+)(ième|ère|ème|er|e)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e)}),era:Pt({matchPatterns:{narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^av/i,/^ap/i]},defaultParseWidth:"any"}),quarter:Pt({matchPatterns:{narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:Pt({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Pt({matchPatterns:{narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Pt({matchPatterns:{narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}};const Si=new class{constructor(){n(this,"currentUser",null),this.currentUser=Ja.getCurrentUser()}login(e,t){const n=Ja.getUsers().find(n=>n.email===e&&n.motDePasse===t&&n.actif);return n?(this.currentUser=n,Ja.setCurrentUser(n),{success:!0,user:n}):{success:!1,message:"Email ou mot de passe incorrect"}}logout(){this.currentUser=null,Ja.remove("currentUser")}getCurrentUser(){return this.currentUser}isAuthenticated(){return null!==this.currentUser}getUserPermissions(e){var t;switch(e||(null==(t=this.currentUser)?void 0:t.role)){case"super_admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!0,canViewSettings:!0,canManageSettings:!0,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!0};case"admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!1,canViewSettings:!0,canManageSettings:!1,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!1};case"employee":return{canViewDashboard:!1,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1};default:return{canViewDashboard:!1,canViewProducts:!1,canManageProducts:!1,canViewSales:!1,canManageSales:!1,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1}}}hasPermission(e){if(!this.currentUser)return!1;return this.getUserPermissions()[e]}hasRole(e){return!!this.currentUser&&e.includes(this.currentUser.role)}canAccessRoute(e){if(!this.currentUser)return!1;const t=this.getUserPermissions();switch(e){case"/dashboard":return t.canViewDashboard;case"/products":return t.canViewProducts;case"/sales":return t.canViewSales;case"/debts":return t.canViewDebts;case"/reports":return t.canViewReports;case"/users":return t.canViewUsers;case"/settings":return t.canViewSettings;case"/expenses":return t.canViewExpenses;default:return!0}}getNavigationItems(){if(!this.currentUser)return[];const e=this.getUserPermissions(),t=[];return e.canViewDashboard&&t.push({label:"Tableau de bord",path:"/dashboard",icon:"Dashboard"}),e.canViewProducts&&t.push({label:"Inventaire",path:"/products",icon:"Inventory"}),e.canViewSales&&t.push({label:"Ventes",path:"/sales",icon:"PointOfSale"}),e.canViewDebts&&t.push({label:"Dettes",path:"/debts",icon:"AccountBalance"}),e.canViewExpenses&&t.push({label:"Dépenses",path:"/expenses",icon:"Receipt"}),e.canViewReports&&t.push({label:"Rapports",path:"/reports",icon:"Assessment"}),e.canViewUsers&&t.push({label:"Utilisateurs",path:"/users",icon:"People"}),e.canViewSettings&&t.push({label:"Paramètres",path:"/settings",icon:"Settings"}),t}updateProfile(e){if(!this.currentUser)return{success:!1,message:"Utilisateur non connecté"};try{const t=Ja.getUsers(),n=t.findIndex(e=>e.id===this.currentUser.id);if(-1===n)return{success:!1,message:"Utilisateur non trouvé"};const a={...t[n],...e};return t[n]=a,Ja.setUsers(t),this.currentUser=a,Ja.setCurrentUser(a),{success:!0}}catch(fr){return{success:!1,message:"Erreur lors de la mise à jour du profil"}}}changePassword(e,t){return this.currentUser?this.currentUser.motDePasse!==e?{success:!1,message:"Mot de passe actuel incorrect"}:t.length<6?{success:!1,message:"Le nouveau mot de passe doit contenir au moins 6 caractères"}:this.updateProfile({motDePasse:t}):{success:!1,message:"Utilisateur non connecté"}}};const vi=new class{constructor(){n(this,"STORAGE_KEY","notifications"),n(this,"LOW_STOCK_THRESHOLD",5),n(this,"notifications",[]),n(this,"listeners",[]),this.loadNotifications()}loadNotifications(){try{const e=localStorage.getItem(this.STORAGE_KEY);this.notifications=e?JSON.parse(e):[]}catch(fr){console.error("Erreur lors du chargement des notifications:",fr),this.notifications=[]}}saveNotifications(){try{localStorage.setItem(this.STORAGE_KEY,JSON.stringify(this.notifications)),this.notifyListeners()}catch(fr){console.error("Erreur lors de la sauvegarde des notifications:",fr)}}notifyListeners(){this.listeners.forEach(e=>e(this.notifications))}subscribe(e){return this.listeners.push(e),e(this.notifications),()=>{const t=this.listeners.indexOf(e);t>-1&&this.listeners.splice(t,1)}}getNotifications(){return[...this.notifications]}getUnreadCount(){return this.notifications.filter(e=>!e.lu).length}markAsRead(e){const t=this.notifications.find(t=>t.id===e);t&&!t.lu&&(t.lu=!0,this.saveNotifications())}markAllAsRead(){let e=!1;this.notifications.forEach(t=>{t.lu||(t.lu=!0,e=!0)}),e&&this.saveNotifications()}deleteNotification(e){const t=this.notifications.findIndex(t=>t.id===e);t>-1&&(this.notifications.splice(t,1),this.saveNotifications())}clearAll(){this.notifications=[],this.saveNotifications()}shouldReceiveStockNotifications(e){if(!e)return!1;const t=Si.getUserPermissions();return"admin"===e.role||"super_admin"===e.role||!0===(null==t?void 0:t.canManageProducts)}calculateSuggestedReorder(e){return Math.max(3*e.stockMin,20)-e.stock}checkLowStock(e){const t=Si.getCurrentUser();if(!this.shouldReceiveStockNotifications(t))return;e.filter(e=>e.stock>0&&e.stock<=this.LOW_STOCK_THRESHOLD).forEach(e=>{this.notifications.find(t=>"warning"===t.type&&t.titre.includes(e.nom)&&t.message.includes("stock bas")&&!t.lu&&Date.now()-new Date(t.dateCreation).getTime()<864e5)||this.createLowStockNotification(e)})}createLowStockNotification(e){const t=this.calculateSuggestedReorder(e),n={id:`stock-${e.id}-${Date.now()}`,type:"warning",titre:`Stock bas: ${e.nom}`,message:`Le produit "${e.nom}" a un stock critique de ${e.stock} unité(s). Stock minimum: ${e.stockMin}. Quantité suggérée à commander: ${t} unité(s).`,dateCreation:(new Date).toISOString(),lu:!1,productId:e.id,productName:e.nom,currentStock:e.stock,minimumStock:e.stockMin,suggestedReorder:t};this.notifications.unshift(n),this.saveNotifications(),this.showBrowserNotification(n)}showBrowserNotification(e){"Notification"in window&&"granted"===Notification.permission&&new Notification(`SmartBoutique - ${e.titre}`,{body:`Stock actuel: ${e.currentStock} unité(s). Commande suggérée: ${e.suggestedReorder} unité(s).`,icon:"/favicon.ico",tag:`stock-${e.productId}`})}async requestNotificationPermission(){if(!("Notification"in window))return!1;if("granted"===Notification.permission)return!0;if("denied"===Notification.permission)return!1;return"granted"===await Notification.requestPermission()}createNotification(e,t,n){const a={id:`notif-${Date.now()}`,type:e,titre:t,message:n,dateCreation:(new Date).toISOString(),lu:!1};this.notifications.unshift(a),this.saveNotifications()}},Ci=({color:e="inherit"})=>{const[t,n]=Ct.useState(null),{notifications:r,unreadCount:s,markAsRead:l,markAllAsRead:d,deleteNotification:u,clearAll:m,requestPermission:j}=(()=>{const[e,t]=Ct.useState([]),[n,a]=Ct.useState(0);return Ct.useEffect(()=>vi.subscribe(e=>{t(e),a(vi.getUnreadCount())}),[]),{notifications:e,unreadCount:n,markAsRead:e=>{vi.markAsRead(e)},markAllAsRead:()=>{vi.markAllAsRead()},deleteNotification:e=>{vi.deleteNotification(e)},clearAll:()=>{vi.clearAll()},requestPermission:async()=>await vi.requestNotificationPermission()}})(),S=e=>{switch(e){case"info":default:return a.jsx(B,{color:"info"});case"warning":return a.jsx(z,{color:"warning"});case"error":return a.jsx($,{color:"error"});case"success":return a.jsx(_,{color:"success"})}},v=e=>{switch(e){case"info":return"info";case"warning":return"warning";case"error":return"error";case"success":return"success";default:return"default"}},C=Boolean(t),b=C?"notification-popover":void 0;return a.jsxs(a.Fragment,{children:[a.jsx(F,{title:"Notifications",children:a.jsx(U,{color:e,onClick:e=>{n(e.currentTarget)},children:a.jsx(T,{badgeContent:s,color:"error",children:s>0?a.jsx(k,{}):a.jsx(R,{})})})}),a.jsx(M,{id:b,open:C,anchorEl:t,onClose:()=>{n(null)},anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},children:a.jsxs(c,{sx:{width:400,maxHeight:500},children:[a.jsxs(i,{sx:{p:2,borderBottom:"1px solid",borderColor:"divider"},children:[a.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[a.jsx(o,{variant:"h6",children:"Notifications"}),s>0&&a.jsx(A,{label:`${s} non lue(s)`,color:"primary",size:"small"})]}),r.length>0&&a.jsxs(i,{sx:{mt:1,display:"flex",gap:1},children:[s>0&&a.jsx(I,{size:"small",startIcon:a.jsx(N,{}),onClick:d,children:"Tout marquer lu"}),a.jsx(I,{size:"small",startIcon:a.jsx(L,{}),onClick:m,color:"error",children:"Tout effacer"})]})]}),"Notification"in window&&"default"===Notification.permission&&a.jsx(O,{severity:"info",sx:{m:1},action:a.jsx(I,{size:"small",onClick:async()=>{await j()},children:"Activer"}),children:"Activez les notifications du navigateur pour les alertes de stock"}),0===r.length?a.jsxs(i,{sx:{p:3,textAlign:"center"},children:[a.jsx(R,{sx:{fontSize:48,color:"text.secondary",mb:1}}),a.jsx(o,{variant:"body2",color:"text.secondary",children:"Aucune notification"})]}):a.jsx(p,{sx:{maxHeight:350,overflow:"auto"},children:r.map((e,t)=>a.jsxs(ft.Fragment,{children:[a.jsxs(x,{button:!0,onClick:()=>(e=>{e.lu||l(e.id)})(e),sx:{bgcolor:e.lu?"transparent":"action.hover","&:hover":{bgcolor:"action.selected"}},children:[a.jsx(g,{children:e.titre.includes("Stock bas")?a.jsx(D,{color:"warning"}):S(e.type)}),a.jsx(y,{primary:a.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[a.jsx(o,{variant:"subtitle2",sx:{fontWeight:e.lu?"normal":"bold",flex:1},children:e.titre}),a.jsx(A,{label:e.type,size:"small",color:v(e.type),variant:"outlined"})]}),secondary:a.jsxs(i,{children:[a.jsx(o,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:e.message}),a.jsx(o,{variant:"caption",color:"text.secondary",children:Ut(new Date(e.dateCreation),"dd/MM/yyyy HH:mm",{locale:Di})})]})}),a.jsx(V,{children:a.jsx(F,{title:"Supprimer",children:a.jsx(U,{edge:"end",size:"small",onClick:t=>{return n=t,a=e.id,n.stopPropagation(),void u(a);var n,a},children:a.jsx(q,{fontSize:"small"})})})})]}),t<r.length-1&&a.jsx(h,{})]},e.id))})]})})]})},bi=240,fi=({currentUser:e,onLogout:t})=>{var n,c,d;if(La())return a.jsx(pi,{currentUser:e,onLogout:t});const[u,T]=Ct.useState(!1),[k,R]=Ct.useState(null),M=Kn(),A=Gn(),I=()=>{T(!u)},N=()=>{R(null)},L=hi.getNavigationItems(),O=e=>{switch(e){case"Dashboard":default:return a.jsx(b,{});case"Inventory":return a.jsx(D,{});case"PointOfSale":return a.jsx(S,{});case"AccountBalance":return a.jsx(v,{});case"Receipt":return a.jsx(f,{});case"Payment":return a.jsx(G,{});case"Assessment":return a.jsx(w,{});case"People":return a.jsx(E,{});case"Settings":return a.jsx(P,{})}},V=a.jsxs("div",{children:[a.jsx(s,{children:a.jsxs(i,{display:"flex",alignItems:"center",width:"100%",children:[a.jsx(o,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:"SmartBoutique"}),a.jsx(U,{onClick:I,sx:{display:{sm:"none"}},children:a.jsx(W,{})})]})}),a.jsx(h,{}),a.jsx(p,{children:L.map(e=>a.jsx(x,{disablePadding:!0,children:a.jsxs(X,{selected:A.pathname===e.path,onClick:()=>{M(e.path),T(!1)},children:[a.jsx(g,{children:O(e.icon)}),a.jsx(y,{primary:e.label})]})},e.path))})]});return a.jsxs(i,{sx:{display:"flex"},children:[a.jsx(r,{position:"fixed",sx:{width:{sm:"calc(100% - 240px)"},ml:{sm:"240px"}},children:a.jsxs(s,{children:[a.jsx(U,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:I,sx:{mr:2,display:{sm:"none"}},children:a.jsx(C,{})}),a.jsx(o,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:(null==(n=L.find(e=>e.path===A.pathname))?void 0:n.label)||"SmartBoutique"}),a.jsx(Ci,{color:"inherit"}),a.jsx(F,{title:"Profil utilisateur",children:a.jsx(U,{color:"inherit",onClick:e=>{R(e.currentTarget)},sx:{ml:1},children:a.jsx(l,{sx:{width:32,height:32,bgcolor:"secondary.main"},children:(null==(d=null==(c=null==e?void 0:e.nom)?void 0:c.charAt(0))?void 0:d.toUpperCase())||"U"})})}),a.jsxs(Q,{anchorEl:k,open:Boolean(k),onClose:N,onClick:N,children:[a.jsx(J,{disabled:!0,children:a.jsxs(i,{children:[a.jsx(o,{variant:"subtitle2",children:null==e?void 0:e.nom}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["super_admin"===(null==e?void 0:e.role)&&"Super Administrateur","admin"===(null==e?void 0:e.role)&&"Administrateur","employee"===(null==e?void 0:e.role)&&"Employé"]})]})}),a.jsx(h,{}),a.jsxs(J,{onClick:()=>M("/settings"),children:[a.jsx(g,{children:a.jsx(H,{fontSize:"small"})}),"Mon Profil"]}),a.jsxs(J,{onClick:()=>{N(),t(),M("/login")},children:[a.jsx(g,{children:a.jsx(j,{fontSize:"small"})}),"Déconnexion"]})]})]})}),a.jsxs(i,{component:"nav",sx:{width:{sm:bi},flexShrink:{sm:0}},children:[a.jsx(m,{variant:"temporary",open:u,onClose:I,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",sm:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:bi}},children:V}),a.jsx(m,{variant:"permanent",sx:{display:{xs:"none",sm:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:bi}},open:!0,children:V})]}),a.jsxs(i,{component:"main",sx:{flexGrow:1,p:3,width:{sm:"calc(100% - 240px)"},minHeight:"100vh",backgroundColor:"background.default"},children:[a.jsx(s,{}),a.jsx(da,{})]})]})},wi=({children:e,requiredPermission:t,requiredRole:n})=>hi.getCurrentUser()?n&&!hi.hasRole(n)?a.jsx(i,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",children:a.jsxs(c,{sx:{p:4,textAlign:"center",maxWidth:400},children:[a.jsx(Y,{sx:{fontSize:64,color:"error.main",mb:2}}),a.jsx(o,{variant:"h5",gutterBottom:!0,children:"Accès Refusé"}),a.jsx(o,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Vous n'avez pas les permissions nécessaires pour accéder à cette page."}),a.jsx(O,{severity:"warning",sx:{mt:2},children:"Contactez votre administrateur pour obtenir l'accès requis."})]})}):t&&!hi.hasPermission(t)?a.jsx(i,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",children:a.jsxs(c,{sx:{p:4,textAlign:"center",maxWidth:400},children:[a.jsx(Y,{sx:{fontSize:64,color:"error.main",mb:2}}),a.jsx(o,{variant:"h5",gutterBottom:!0,children:"Accès Refusé"}),a.jsx(o,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Vous n'avez pas les permissions nécessaires pour accéder à cette page."}),a.jsx(O,{severity:"warning",sx:{mt:2},children:"Cette fonctionnalité est réservée aux utilisateurs autorisés."})]})}):a.jsx(a.Fragment,{children:e}):a.jsx(ca,{to:"/login",replace:!0}),Ei=({onLogin:e})=>{const[t,n]=Ct.useState(""),[r,s]=Ct.useState(""),[c,d]=Ct.useState(!1),[u,m]=Ct.useState(""),[h,p]=Ct.useState(!1),x=async t=>{p(!0),m("");let n={email:"",password:""};switch(t){case"admin":n={email:"<EMAIL>",password:"admin123"};break;case"manager":n={email:"<EMAIL>",password:"manager123"};break;case"employee":n={email:"<EMAIL>",password:"employee123"}}try{const t=await hi.login(n.email,n.password);t.success&&t.user?e(t.user):m(t.message||"Erreur de connexion")}catch(a){m("Erreur de connexion. Veuillez réessayer.")}finally{p(!1)}};return a.jsx(K,{maxWidth:"sm",sx:{height:"100vh",display:"flex",alignItems:"center"},children:a.jsxs(i,{sx:{width:"100%",px:2},children:[a.jsxs(i,{sx:{textAlign:"center",mb:4},children:[a.jsx(l,{sx:{width:80,height:80,bgcolor:"primary.main",mx:"auto",mb:2},children:a.jsx(Z,{sx:{fontSize:40}})}),a.jsx(o,{variant:"h4",component:"h1",fontWeight:"bold",color:"primary",children:"SmartBoutique"}),a.jsx(o,{variant:"subtitle1",color:"text.secondary",sx:{mt:1},children:"Gestion de boutique mobile"})]}),a.jsx(ee,{elevation:4,sx:{borderRadius:3},children:a.jsxs(te,{sx:{p:4},children:[a.jsx(o,{variant:"h5",component:"h2",textAlign:"center",sx:{mb:3},children:"Connexion"}),u&&a.jsx(O,{severity:"error",sx:{mb:3},children:u}),a.jsxs(i,{component:"form",onSubmit:async n=>{n.preventDefault(),m(""),p(!0);try{const n=await hi.login(t,r);n.success&&n.user?e(n.user):m(n.message||"Erreur de connexion")}catch(a){m("Erreur de connexion. Veuillez réessayer.")}finally{p(!1)}},children:[a.jsx(ne,{fullWidth:!0,label:"Email",type:"email",value:t,onChange:e=>n(e.target.value),required:!0,sx:{mb:3},InputProps:{startAdornment:a.jsx(ae,{position:"start",children:a.jsx(ie,{color:"action"})}),style:{fontSize:"16px"}},inputProps:{autoComplete:"email",style:{fontSize:"16px"}}}),a.jsx(ne,{fullWidth:!0,label:"Mot de passe",type:c?"text":"password",value:r,onChange:e=>s(e.target.value),required:!0,sx:{mb:4},InputProps:{startAdornment:a.jsx(ae,{position:"start",children:a.jsx(Y,{color:"action"})}),endAdornment:a.jsx(ae,{position:"end",children:a.jsx(U,{onClick:()=>{d(!c)},edge:"end",size:"large",children:c?a.jsx(re,{}):a.jsx(se,{})})}),style:{fontSize:"16px"}},inputProps:{autoComplete:"current-password",style:{fontSize:"16px"}}}),a.jsx(I,{type:"submit",fullWidth:!0,variant:"contained",size:"large",disabled:h,sx:{py:2,fontSize:"1.1rem",fontWeight:"bold",borderRadius:2,mb:3},children:h?"Connexion...":"Se connecter"})]}),a.jsxs(i,{sx:{mt:3},children:[a.jsx(o,{variant:"body2",color:"text.secondary",textAlign:"center",sx:{mb:2},children:"Connexion rapide (Démo)"}),a.jsxs(i,{sx:{display:"flex",flexDirection:"column",gap:1},children:[a.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("admin"),disabled:h,sx:{py:1.5},children:"Super Admin"}),a.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("manager"),disabled:h,sx:{py:1.5},children:"Gestionnaire"}),a.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("employee"),disabled:h,sx:{py:1.5},children:"Employé"})]})]})]})}),a.jsx(o,{variant:"body2",color:"text.secondary",textAlign:"center",sx:{mt:3},children:"Version Mobile • SmartBoutique 2024"})]})})},Pi=({onLogin:e})=>{if(La())return a.jsx(Ei,{onLogin:e});const[t,n]=Ct.useState(""),[r,s]=Ct.useState(""),[c,d]=Ct.useState(""),[u,m]=Ct.useState(!1),p=(e,t)=>{n(e),s(t)};return a.jsx(K,{component:"main",maxWidth:"sm",children:a.jsxs(i,{sx:{minHeight:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",py:4},children:[a.jsxs(i,{sx:{mb:4,textAlign:"center"},children:[a.jsx(l,{sx:{mx:"auto",mb:2,bgcolor:"primary.main",width:64,height:64},children:a.jsx(Z,{sx:{fontSize:32}})}),a.jsx(o,{component:"h1",variant:"h4",gutterBottom:!0,children:"SmartBoutique"}),a.jsx(o,{variant:"subtitle1",color:"text.secondary",children:"Système de Gestion de Boutique"})]}),a.jsx(ee,{sx:{width:"100%",maxWidth:400},children:a.jsxs(te,{sx:{p:4},children:[a.jsxs(i,{sx:{display:"flex",alignItems:"center",mb:3},children:[a.jsx(l,{sx:{mr:2,bgcolor:"secondary.main"},children:a.jsx(oe,{})}),a.jsx(o,{component:"h2",variant:"h5",children:"Connexion"})]}),c&&a.jsx(O,{severity:"error",sx:{mb:2},children:c}),a.jsxs(i,{component:"form",onSubmit:async n=>{n.preventDefault(),d(""),m(!0);try{const n=await hi.login(t,r);n.success&&n.user?e(n.user):d(n.message||"Erreur de connexion")}catch(a){d("Une erreur est survenue lors de la connexion")}finally{m(!1)}},children:[a.jsx(ne,{margin:"normal",required:!0,fullWidth:!0,id:"email",label:"Adresse email",name:"email",autoComplete:"email",autoFocus:!0,value:t,onChange:e=>n(e.target.value),disabled:u}),a.jsx(ne,{margin:"normal",required:!0,fullWidth:!0,name:"password",label:"Mot de passe",type:"password",id:"password",autoComplete:"current-password",value:r,onChange:e=>s(e.target.value),disabled:u}),a.jsx(I,{type:"submit",fullWidth:!0,variant:"contained",sx:{mt:3,mb:2},disabled:u,children:u?"Connexion...":"Se connecter"})]}),a.jsx(h,{sx:{my:3},children:a.jsx(A,{label:"Comptes de démonstration",size:"small"})}),a.jsxs(i,{sx:{display:"flex",flexDirection:"column",gap:1},children:[a.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","admin123"),disabled:u,children:"Super Admin (<EMAIL>)"}),a.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","manager123"),disabled:u,children:"Gestionnaire (<EMAIL>)"}),a.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","employee123"),disabled:u,children:"Employé (<EMAIL>)"})]})]})}),a.jsxs(i,{sx:{mt:4,textAlign:"center"},children:[a.jsx(o,{variant:"body2",color:"text.secondary",children:"© 2024 SmartBoutique. Tous droits réservés."}),a.jsx(o,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Version 1.0.0"})]})]})})},Fi=2800,Ui=e=>null==e||isNaN(e)?0:Math.round(100*e)/100,Ti=(e,t,n,a=2800)=>{if(null==e||isNaN(e))return 0;if((null==a||isNaN(a)||a<=0)&&(console.warn("Invalid exchange rate provided, using default:",Fi),a=Fi),t===n)return Ui(e);let i;if("CDF"===t&&"USD"===n)i=e/a;else{if("USD"!==t||"CDF"!==n)return console.error("Unsupported currency conversion:",t,"to",n),0;i=e*a}return Ui(i)},ki=(e,t=2800)=>Ti(e,"CDF","USD",t),Ri=(e,t=0,n=2)=>null==e||isNaN(e)?"0":e.toLocaleString("fr-FR",{minimumFractionDigits:t,maximumFractionDigits:n}),Mi=(e,t,n={})=>{const{showSymbol:a=!0,minimumFractionDigits:i=("USD"===t?2:0),maximumFractionDigits:r=2}=n,s=null==e||isNaN(e)?0:e,o=Ri(s,i,r);return a?"USD"===t?`$${o}`:"CDF"===t?`${o} CDF`:`${o} ${t}`:o},Ai=(e,t="valeur",n={})=>{const{allowZero:a=!1,allowNegative:i=!1,minValue:r=null,maxValue:s=null}=n,o=[];return null==e||isNaN(e)?(o.push(`${t} doit être un nombre valide`),{isValid:!1,errors:o}):(!i&&e<0&&o.push(`${t} ne peut pas être négatif`),a||0!==e||o.push(`${t} doit être supérieur à zéro`),null!==r&&e<r&&o.push(`${t} doit être supérieur ou égal à ${r}`),null!==s&&e>s&&o.push(`${t} doit être inférieur ou égal à ${s}`),{isValid:0===o.length,errors:o})},Ii=(e,t=0,n="Erreur de calcul financier")=>{try{const n=e();return null==n||isNaN(n)?t:n}catch(fr){return console.error(n,fr),t}},Ni="Le prix ne peut pas être négatif",Li=(e,t,n=2800)=>{const a=Ui((i=t,r=e,Ii(()=>i-r,0,"Erreur lors du calcul du bénéfice unitaire")));var i,r;return{beneficeUnitaireCDF:a,beneficeUnitaireUSD:Ui(ki(a,n))}},Oi=(e,t)=>{const n=((e,t,n={})=>{const{purchaseFieldName:a="Le prix d'achat",sellingFieldName:i="Le prix de vente"}=n,r=Ai(e,a),s=Ai(t,i),o=[...r.errors,...s.errors];return r.isValid&&s.isValid&&t<=e&&o.push("Le prix de vente doit être supérieur au prix d'achat"),{isValid:0===o.length,errors:o}})(e,t);return{isValid:n.isValid,errorMessage:n.errors.length>0?n.errors[0]:void 0}},Vi=(e,t)=>"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`,qi=(e,t)=>{const n=(e=>Ii(()=>{let t=0,n=0;return e.forEach(e=>{e.beneficeUnitaireCDF&&e.stock>0&&(t+=Ui(e.beneficeUnitaireCDF*e.stock)),e.beneficeUnitaireUSD&&e.stock>0&&(n+=Ui(e.beneficeUnitaireUSD*e.stock))}),{totalRevenueCDF:Ui(t),totalRevenueUSD:Ui(n)}},{totalRevenueCDF:0,totalRevenueUSD:0},"Erreur lors du calcul des revenus d'inventaire"))(e),a=((e,t)=>Ii(()=>{let n=0,a=0;return e.forEach(e=>{e.produits.forEach(e=>{const i=t.find(t=>t.id===e.produitId);i&&i.beneficeUnitaireCDF&&(n+=Ui(i.beneficeUnitaireCDF*e.quantite)),i&&i.beneficeUnitaireUSD&&(a+=Ui(i.beneficeUnitaireUSD*e.quantite))})}),{realizedRevenueCDF:Ui(n),realizedRevenueUSD:Ui(a)}},{realizedRevenueCDF:0,realizedRevenueUSD:0},"Erreur lors du calcul des revenus réalisés"))(t,e),i=(e=>{const t={};return e.forEach(e=>{t[e.categorie]||(t[e.categorie]={revenueCDF:0,revenueUSD:0,productCount:0}),e.beneficeUnitaireCDF&&e.stock>0&&(t[e.categorie].revenueCDF+=e.beneficeUnitaireCDF*e.stock),e.beneficeUnitaireUSD&&e.stock>0&&(t[e.categorie].revenueUSD+=e.beneficeUnitaireUSD*e.stock),t[e.categorie].productCount+=1}),t})(e),r=((e,t=10)=>e.filter(e=>e.beneficeUnitaireCDF&&e.beneficeUnitaireCDF>0).sort((e,t)=>{const n=(e.beneficeUnitaireCDF||0)*e.stock;return(t.beneficeUnitaireCDF||0)*t.stock-n}).slice(0,t))(e,5);return{totalInventoryRevenueCDF:n.totalRevenueCDF,totalInventoryRevenueUSD:n.totalRevenueUSD,realizedRevenueCDF:a.realizedRevenueCDF,realizedRevenueUSD:a.realizedRevenueUSD,categoryBreakdown:i,topProducts:r}},Bi=(e,t)=>Mi(e,t),_i=(e,t=2800)=>{const n=null==e||isNaN(e)?0:e,a=null==t||isNaN(t)||t<=0?Fi:t,i=Ui(ki(n,a));return{primaryAmount:Mi(n,"CDF",{showSymbol:!1}),secondaryAmount:Mi(i,"USD",{showSymbol:!1}),primaryCurrency:"CDF",secondaryCurrency:"USD"}},$i=e=>0===e.stock?"out_of_stock":e.stock<=e.stockMin?"low_stock":"in_stock",zi=()=>"123"+Date.now().toString().slice(-10),Wi=()=>{const e=Date.now().toString(),t=Math.random().toString(36).substr(2,4).toUpperCase();return`SB${e.slice(-8)}${t}`},Xi=(e,t)=>ki(e,t),Qi=(e,t)=>((e,t=2800)=>Ti(e,"USD","CDF",t))(e,t),Ji=(e,t)=>ki(e,t),Hi=({analytics:e,products:t,todaySalesData:n,debtsData:r,isLoading:s=!1})=>{if(s)return a.jsxs(i,{children:[a.jsx(o,{variant:"h5",gutterBottom:!0,children:"Profit - Analyse des Bénéfices"}),a.jsx(le,{})]});if(0===e.totalProductsWithProfit)return a.jsxs(i,{children:[a.jsx(o,{variant:"h5",gutterBottom:!0,children:"Profit - Analyse des Bénéfices"}),a.jsx(O,{severity:"info",children:"Aucun produit avec prix d'achat et de vente configurés. Ajoutez des prix d'achat aux produits pour voir l'analyse des profits."})]});const l=Object.entries(e.categoryBreakdown);return a.jsxs(i,{children:[a.jsx(o,{variant:"h5",gutterBottom:!0,sx:{mb:3},children:"Profit - Analyse des Bénéfices"}),a.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Profit Potentiel"}),a.jsxs(o,{variant:"h6",fontWeight:"medium",children:[_i(e.totalInventoryRevenueCDF,2800).primaryAmount," ",_i(e.totalInventoryRevenueCDF,2800).primaryCurrency]}),a.jsxs(o,{variant:"body2",color:"primary",children:["≈ $",_i(e.totalInventoryRevenueCDF,2800).secondaryAmount]})]}),a.jsx(D,{color:"primary",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Profit Réalisé"}),a.jsxs(o,{variant:"h6",fontWeight:"medium",children:[_i(e.realizedRevenueCDF,2800).primaryAmount," ",_i(e.realizedRevenueCDF,2800).primaryCurrency]}),a.jsxs(o,{variant:"body2",color:"success.main",children:["≈ $",_i(e.realizedRevenueCDF,2800).secondaryAmount]})]}),a.jsx(de,{color:"success",sx:{fontSize:40}})]})})})}),n&&a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes du jour"}),a.jsxs(o,{variant:"h6",fontWeight:"medium",children:[_i(n.revenusCDF,2800).primaryAmount," ",_i(n.revenusCDF,2800).primaryCurrency]}),a.jsxs(o,{variant:"body2",color:"primary",children:["≈ $",_i(n.revenusCDF,2800).secondaryAmount]}),a.jsxs(o,{variant:"caption",color:"textSecondary",children:[n.nombreVentes," vente",1!==n.nombreVentes?"s":""]})]}),a.jsx(S,{color:"primary",sx:{fontSize:40}})]})})})}),r&&a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total des Dettes"}),a.jsxs(o,{variant:"h6",color:"error",fontWeight:"medium",children:[_i(r.montantDettesTotalCDF,2800).primaryAmount," ",_i(r.montantDettesTotalCDF,2800).primaryCurrency]}),a.jsxs(o,{variant:"body2",color:"error",children:["≈ $",_i(r.montantDettesTotalCDF,2800).secondaryAmount]}),a.jsxs(o,{variant:"caption",color:"textSecondary",children:[r.dettesActives," dette",1!==r.dettesActives?"s":""," active",1!==r.dettesActives?"s":""]})]}),a.jsx(v,{color:"warning",sx:{fontSize:40}})]})})})})]}),a.jsxs(ce,{container:!0,spacing:3,children:[a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Produits les Plus Rentables"}),a.jsx(te,{children:a.jsx(me,{children:a.jsxs(he,{size:"small",children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:"Produit"}),a.jsx(ge,{align:"right",children:"Profit Potentiel"})]})}),a.jsx(ye,{children:e.topProducts.map(e=>{const t=(e.beneficeUnitaireCDF||0)*e.stock;return a.jsxs(xe,{children:[a.jsx(ge,{children:a.jsxs(i,{children:[a.jsx(o,{variant:"body2",fontWeight:"medium",children:e.nom}),a.jsxs(o,{variant:"caption",color:"textSecondary",children:["Stock: ",e.stock]})]})}),a.jsx(ge,{align:"right",children:a.jsx(o,{variant:"body2",children:Vi(t,"CDF")})})]},e.id)})})]})})})]})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Profit par Catégorie"}),a.jsx(te,{children:a.jsx(me,{children:a.jsxs(he,{size:"small",children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:"Catégorie"}),a.jsx(ge,{align:"right",children:"Produits"}),a.jsx(ge,{align:"right",children:"Profit (CDF)"})]})}),a.jsx(ye,{children:l.sort(([,e],[,t])=>t.revenueCDF-e.revenueCDF).map(([e,t])=>a.jsxs(xe,{children:[a.jsx(ge,{children:a.jsx(A,{label:e,size:"small"})}),a.jsx(ge,{align:"right",children:t.productCount}),a.jsxs(ge,{align:"right",children:[a.jsx(o,{variant:"body2",children:Vi(t.revenueCDF,"CDF")}),a.jsx(o,{variant:"caption",color:"textSecondary",children:Vi(t.revenueUSD,"USD")})]})]},e))})]})})})]})})]})]})},Gi=()=>{const[e,t]=Ct.useState(null),[n,r]=Ct.useState([]),[s,l]=Ct.useState([]),[d,u]=Ct.useState(null),[m,h]=Ct.useState(null),[j,v]=Ct.useState(null),[C,b]=Ct.useState({tauxChangeUSDCDF:2800}),[f,w]=Ct.useState("jour"),E=hi.getUserPermissions(),P=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(fr){return console.warn("Error parsing date:",e,fr),null}};Ct.useEffect(()=>{(async()=>{try{const e=await mi.getSettings();b(e),await U()}catch(fr){console.error("Error loading data:",fr),await U()}})()},[]);const F=(e,t)=>{switch(e){case"jour":default:return t.ventesDuJour;case"semaine":return t.ventesDeLaSemaine;case"mois":return t.ventesDuMois}},U=async()=>{const e=await mi.getProducts(),n=await mi.getSales(),a=await mi.getDebts(),i=await mi.getSettings(),s=new Date,o=Tt(s),c=kt(s),d=Rt(s,7),u=Rt(s,30),m=n.filter(e=>{const t=P(e.datevente);return!!t&&(Mt(t,o)&&At(t,c))}),h=n.filter(e=>{const t=P(e.datevente);return!!t&&Mt(t,d)}),p=n.filter(e=>{const t=P(e.datevente);return!!t&&Mt(t,u)}),x=e.filter(e=>e.stock>0),g=e.filter(e=>e.stock<=e.stockMin&&e.stock>0),y=e.filter(e=>0===e.stock),j=a.filter(e=>"active"===e.statut),D=a.filter(e=>"overdue"===e.statut),S=Ui(m.reduce((e,t)=>e+t.totalCDF,0)),C=Ui(h.reduce((e,t)=>e+t.totalCDF,0)),b=Ui(p.reduce((e,t)=>e+t.totalCDF,0)),f=Ui(e.reduce((e,t)=>e+t.prixCDF*t.stock,0)),w=Ui(j.reduce((e,t)=>e+t.montantRestantCDF,0)),F=i.tauxChangeUSDCDF||Fi,U={ventesDuJour:{nombreVentes:m.length,revenusCDF:S,revenusUSD:ki(S,F)},ventesDeLaSemaine:{nombreVentes:h.length,revenusCDF:C,revenusUSD:ki(C,F)},ventesDuMois:{nombreVentes:p.length,revenusCDF:b,revenusUSD:ki(b,F)},articlesActifs:x.length,produitsStockBas:g.length,articlesEnRupture:y.length,valeurInventaireCDF:f,valeurInventaireUSD:f/i.tauxChangeUSDCDF,dettesActives:j.length,dettesEnRetard:D.length,montantDettesTotalCDF:w,montantDettesTotalUSD:w/i.tauxChangeUSDCDF};if(t(U),r(g.slice(0,5)),l(n.slice(-5).reverse()),E.canViewRevenue){const t=qi(e,n);v(t)}T(n),k(n)},T=e=>{const t=Array.from({length:14},(e,t)=>{const n=Rt(new Date,13-t);return{date:n,label:Ut(n,"dd/MM",{locale:Di}),sales:0,revenue:0}});e.forEach(e=>{const n=P(e.datevente);if(!n)return;const a=t.find(e=>Ut(e.date,"yyyy-MM-dd")===Ut(n,"yyyy-MM-dd"));a&&(a.sales+=1,a.revenue+=e.totalCDF)}),u({labels:t.map(e=>e.label),datasets:[{label:"Profit (CDF)",data:t.map(e=>e.revenue),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1}]})},k=e=>{const t=e.reduce((e,t)=>(e[t.methodePaiement]=(e[t.methodePaiement]||0)+1,e),{});h({labels:["Cash","Banque","Mobile Money"],datasets:[{data:[t.cash||0,t.banque||0,t.mobile_money||0],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]})},R=e=>0===e.stock?a.jsx($,{color:"error"}):e.stock<=e.stockMin?a.jsx(z,{color:"warning"}):a.jsx(_,{color:"success"});return e?a.jsxs(i,{children:[a.jsx(o,{variant:"h4",gutterBottom:!0,children:"Tableau de bord"}),E.canViewRevenue&&j&&a.jsx(i,{sx:{mb:4},children:a.jsx(Hi,{analytics:j,products:n,todaySalesData:null==e?void 0:e.ventesDuJour,debtsData:e?{montantDettesTotalCDF:e.montantDettesTotalCDF,montantDettesTotalUSD:e.montantDettesTotalUSD,dettesActives:e.dettesActives}:void 0,isLoading:!j})}),a.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[a.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:a.jsx(ee,{children:a.jsxs(te,{children:[a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",mb:1,children:[a.jsxs(je,{size:"small",sx:{minWidth:120},children:[a.jsx(De,{children:"Période"}),a.jsxs(Se,{value:f,label:"Période",onChange:e=>w(e.target.value),children:[a.jsx(J,{value:"jour",children:"Jour"}),a.jsx(J,{value:"semaine",children:"Semaine"}),a.jsx(J,{value:"mois",children:"Mois"})]})]}),a.jsx(S,{color:"primary",sx:{fontSize:40}})]}),a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:(e=>{switch(e){case"jour":default:return"Ventes du jour";case"semaine":return"Ventes de la semaine";case"mois":return"Ventes du mois"}})(f)}),a.jsx(o,{variant:"h6",children:F(f,e).nombreVentes}),E.canViewFinancials&&a.jsxs(a.Fragment,{children:[a.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[_i(F(f,e).revenusCDF,C.tauxChangeUSDCDF).primaryAmount," ",_i(F(f,e).revenusCDF,C.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_i(F(f,e).revenusCDF,C.tauxChangeUSDCDF).secondaryAmount]})]})]})]})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes de la semaine"}),a.jsx(o,{variant:"h6",children:e.ventesDeLaSemaine.nombreVentes}),E.canViewFinancials&&a.jsxs(a.Fragment,{children:[a.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[_i(e.ventesDeLaSemaine.revenusCDF,C.tauxChangeUSDCDF).primaryAmount," ",_i(e.ventesDeLaSemaine.revenusCDF,C.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_i(e.ventesDeLaSemaine.revenusCDF,C.tauxChangeUSDCDF).secondaryAmount]})]})]}),a.jsx(ve,{color:"success",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Articles actifs"}),a.jsx(o,{variant:"h6",children:e.articlesActifs}),e.produitsStockBas>0&&a.jsx(A,{label:`${e.produitsStockBas} stock bas`,color:"warning",size:"small"})]}),a.jsx(D,{color:"info",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Valeur Inventaire"}),E.canViewFinancials?a.jsxs(a.Fragment,{children:[a.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:[_i(e.valeurInventaireCDF,C.tauxChangeUSDCDF).primaryAmount," ",_i(e.valeurInventaireCDF,C.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",_i(e.valeurInventaireCDF,C.tauxChangeUSDCDF).secondaryAmount]})]}):a.jsxs(o,{variant:"h6",children:[e.articlesActifs," articles"]})]}),a.jsx(D,{color:"success",sx:{fontSize:40}})]})})})})]}),e.articlesEnRupture>0&&a.jsx(ce,{container:!0,spacing:3,sx:{mb:3},children:a.jsx(ce,{item:!0,xs:12,children:a.jsx(O,{severity:"error",sx:{mb:2},children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{variant:"h6",component:"div",children:"Articles en rupture"}),a.jsxs(o,{variant:"body2",children:[e.articlesEnRupture," article",e.articlesEnRupture>1?"s":""," en rupture de stock nécessite",e.articlesEnRupture>1?"nt":""," un réapprovisionnement urgent"]})]}),a.jsx($,{sx:{fontSize:40}})]})})})}),a.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[a.jsx(ce,{item:!0,xs:12,md:8,children:a.jsxs(c,{sx:{p:2},children:[a.jsx(o,{variant:"h6",gutterBottom:!0,children:"Tendance des ventes (14 derniers jours)"}),d&&a.jsx($t,{data:d,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})]})}),a.jsx(ce,{item:!0,xs:12,md:4,children:a.jsxs(c,{sx:{p:2},children:[a.jsx(o,{variant:"h6",gutterBottom:!0,children:"Méthodes de paiement"}),m&&a.jsx(zt,{data:m,options:{responsive:!0,plugins:{legend:{position:"bottom"}}}})]})})]}),a.jsxs(ce,{container:!0,spacing:3,children:[a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(c,{sx:{p:2},children:[a.jsx(o,{variant:"h6",gutterBottom:!0,children:"Produits en stock bas"}),0===n.length?a.jsx(O,{severity:"success",children:"Aucun produit en stock bas"}):a.jsx(p,{dense:!0,children:n.map(e=>a.jsxs(x,{children:[a.jsx(g,{children:R(e)}),a.jsx(y,{primary:e.nom,secondary:`Stock: ${e.stock} (Min: ${e.stockMin})`})]},e.id))})]})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(c,{sx:{p:2},children:[a.jsx(o,{variant:"h6",gutterBottom:!0,children:"Ventes récentes"}),0===s.length?a.jsx(O,{severity:"info",children:"Aucune vente récente"}):a.jsx(p,{dense:!0,children:s.map(e=>a.jsx(x,{children:a.jsx(y,{primary:E.canViewFinancials?`${e.nomClient} - ${_i(e.totalCDF,C.tauxChangeUSDCDF).primaryAmount} ${_i(e.totalCDF,C.tauxChangeUSDCDF).primaryCurrency}`:e.nomClient,secondary:(()=>{const t=P(e.datevente),n=t?Ut(t,"dd/MM/yyyy HH:mm",{locale:Di}):"Date invalide";if(E.canViewFinancials){return`${n} • ≈ $${_i(e.totalCDF,C.tauxChangeUSDCDF).secondaryAmount}`}return n})()})},e.id))})]})})]})]}):a.jsx(o,{children:"Chargement..."})},Yi=({label:e,value:t,onChange:n,min:r=0,max:s=1e6,step:l=100,exchangeRate:c,disabled:d=!1,required:u=!1,error:m=!1,helperText:h,showSlider:p=!0,allowUSDInput:x=!0,onCurrencyModeChange:g})=>{const[y,j]=Ct.useState("CDF"),[D,S]=Ct.useState(""),[v,C]=Ct.useState(t);Ct.useEffect(()=>{if(C(t),"CDF"===y)S(t.toString());else{const e=Xi(t,c);S(e.toFixed(2))}},[t,y,c]);const b=Xi(v,c),f=v;return a.jsxs(je,{fullWidth:!0,disabled:d,children:[a.jsxs(Ce,{component:"legend",sx:{mb:1},children:[e," ",u&&"*"]}),a.jsxs(ce,{container:!0,spacing:2,children:[x&&a.jsx(ce,{item:!0,xs:12,children:a.jsxs(be,{value:y,exclusive:!0,onChange:(e,t)=>{if(t&&t!==y){if(j(t),"USD"===t){const e=Xi(v,c);S(e.toFixed(2))}else S(v.toString());g&&g(t)}},size:"small",disabled:d,children:[a.jsx(fe,{value:"CDF",children:"Saisie en CDF"}),a.jsx(fe,{value:"USD",children:"Saisie en USD"})]})}),a.jsx(ce,{item:!0,xs:12,md:p?6:12,children:a.jsx(ne,{fullWidth:!0,label:`Montant (${y})`,type:"number",value:D,onChange:e=>{const t=e.target.value;S(t);const a=parseFloat(t)||0;if(Ai(a,"Le montant",{allowZero:!0,allowNegative:!1,minValue:r,maxValue:s}).isValid){let e;e="CDF"===y?a:Qi(a,c),e=Math.max(r,Math.min(s,e)),C(e),n(e)}},disabled:d,error:m,helperText:h,inputProps:{min:"CDF"===y?r:Xi(r,c),max:"CDF"===y?s:Xi(s,c),step:"CDF"===y?l:.01},InputProps:{startAdornment:a.jsx(ae,{position:"start",children:"USD"===y?"$":""}),endAdornment:a.jsx(ae,{position:"end",children:"CDF"===y?"CDF":"USD"})}})}),p&&a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(i,{sx:{px:2,pt:3},children:[a.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Curseur (CDF)"}),a.jsx(we,{value:v,onChange:(e,t)=>{const a=Array.isArray(t)?t[0]:t;if(C(a),"CDF"===y)S(a.toString());else{const e=Xi(a,c);S(e.toFixed(2))}n(a)},min:r,max:s,step:l,disabled:d,valueLabelDisplay:"auto",valueLabelFormat:e=>Bi(e,"CDF")})]})}),a.jsx(ce,{item:!0,xs:12,children:a.jsxs(i,{sx:{p:2,bgcolor:"grey.50",borderRadius:1,border:"1px solid",borderColor:"grey.300"},children:[a.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Équivalences:"}),a.jsx(o,{variant:"body1",color:"primary",fontWeight:"medium",children:Bi(f,"CDF")}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",Bi(b,"USD")]})]})})]})]})},Ki=({value:e,onChange:t,min:n=1,max:r=999999,disabled:s=!1,size:o="small",showButtons:l=!0,allowDirectInput:c=!0,label:d,error:u=!1,helperText:m})=>{const[h,p]=Ct.useState(e.toString());Ct.useEffect(()=>{p(e.toString())},[e]);const x=a=>{const i=a.target.value;if(p(i),""===i)return;const s=parseFloat(i);if(!isNaN(s)){const a=Math.round(s),i=Math.max(n,Math.min(r,a));a>=n&&a<=r?t(a):i!==e&&t(i)}},g=()=>{if(""===h||isNaN(parseFloat(h))){const a=e||n;return p(a.toString()),void(a!==e&&t(a))}const a=Math.round(parseFloat(h)),i=Math.max(n,Math.min(r,a));p(i.toString()),i!==e&&t(i)},y=()=>{const n=Math.min(r,e+1);t(n)},j=()=>{const a=Math.max(n,e-1);t(a)},D=e=>{e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase())||(/[0-9.]/.test(e.key)||["Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Tab","Enter","Home","End","Escape","."].includes(e.key)||e.preventDefault(),"."===e.key&&h.includes(".")&&e.preventDefault())};return!c&&l?a.jsxs(i,{display:"flex",alignItems:"center",gap:.5,children:[a.jsx(U,{size:o,onClick:j,disabled:s||e<=n,children:a.jsx(Ee,{fontSize:o})}),a.jsx(i,{sx:{minWidth:"small"===o?"30px":"40px",textAlign:"center",fontWeight:"medium",fontSize:"small"===o?"0.875rem":"1rem"},children:e}),a.jsx(U,{size:o,onClick:y,disabled:s||e>=r,children:a.jsx(Pe,{fontSize:o})})]}):c&&!l?a.jsx(ne,{size:o,label:d,type:"number",value:h,onChange:x,onBlur:g,onKeyPress:D,disabled:s,error:u,helperText:m,inputProps:{min:n,max:r,step:1},sx:{minWidth:"80px"}}):a.jsx(ne,{size:o,label:d,type:"number",value:h,onChange:x,onBlur:g,onKeyPress:D,disabled:s,error:u,helperText:m||(l?"Tapez directement la quantité (recommandé) ou utilisez +/-":"Tapez directement la quantité désirée"),placeholder:"Tapez la quantité...",inputProps:{min:n,max:r,step:1,style:{textAlign:"center",fontSize:"small"===o?"0.875rem":"1rem",fontWeight:500}},InputProps:{startAdornment:l?a.jsx(ae,{position:"start",children:a.jsx(U,{size:o,onClick:j,disabled:s||e<=n,edge:"start",sx:{opacity:.6,"&:hover":{opacity:.8},transition:"opacity 0.2s ease"},children:a.jsx(Ee,{fontSize:o})})}):void 0,endAdornment:l?a.jsx(ae,{position:"end",children:a.jsx(U,{size:o,onClick:y,disabled:s||e>=r,edge:"end",sx:{opacity:.6,"&:hover":{opacity:.8},transition:"opacity 0.2s ease"},children:a.jsx(Pe,{fontSize:o})})}):void 0},sx:{minWidth:l?"160px":"100px","& .MuiOutlinedInput-root":{"&:hover fieldset":{borderColor:"primary.main"},"&.Mui-focused fieldset":{borderWidth:2,borderColor:"primary.main"},"& input":{cursor:"text","&:focus":{backgroundColor:"rgba(25, 118, 210, 0.04)"}}}}})},Zi=()=>{const[e,t]=Ct.useState([]),[n,r]=Ct.useState([]),[s,l]=Ct.useState([]),[d,u]=Ct.useState(""),[m,h]=Ct.useState(""),[p,x]=Ct.useState("all"),[g,y]=Ct.useState(0),[j,S]=Ct.useState(10),[v,C]=Ct.useState(!1),[b,f]=Ct.useState(null),[w,E]=Ct.useState({nom:"",description:"",prixAchatCDF:0,prixCDF:0,categorie:"",stock:0,stockMin:0,quantiteEnStock:0,coutAchatStockCDF:0,prixParPieceCDF:0}),[P,T]=Ct.useState(""),[k,R]=Ct.useState(""),[M,N]=Ct.useState(!1),[L,V]=Ct.useState(""),[B,W]=Ct.useState({tauxChangeUSDCDF:2800}),X=hi.getUserPermissions();Ct.useEffect(()=>{Q()},[]);Ct.useEffect(()=>{e.length>0&&vi.checkLowStock(e)},[e]),Ct.useEffect(()=>{H()},[e,d,m,p]),Ct.useEffect(()=>{-1===j&&S(s.length||1)},[s.length,j]);const Q=async()=>{try{const e=await mi.getProducts(),n=await mi.getSettings(),a=e.map(e=>{const t=(new Date).toISOString();return{...e,dateCreation:e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())?e.dateCreation:t,dateModification:e.dateModification&&!isNaN(new Date(e.dateModification).getTime())?e.dateModification:t}});t(a),r(n.categories),W(n)}catch(e){console.error("Error loading products:",e),T("Erreur lors du chargement des produits")}},H=()=>{let t=e;d&&(t=t.filter(e=>e.nom.toLowerCase().includes(d.toLowerCase())||e.codeQR.toLowerCase().includes(d.toLowerCase())||e.description.toLowerCase().includes(d.toLowerCase()))),m&&(t=t.filter(e=>e.categorie===m)),"all"!==p&&(t=t.filter(e=>$i(e)===p)),l(t)},G=e=>{switch(e){case"out_of_stock":return"error";case"low_stock":return"warning";case"in_stock":return"success";default:return"default"}},Y=e=>{switch(e){case"out_of_stock":return a.jsx($,{});case"low_stock":return a.jsx(z,{});case"in_stock":return a.jsx(_,{});default:return a.jsx(D,{})}},K=e=>{switch(e){case"out_of_stock":return"Rupture";case"low_stock":return"Stock bas";case"in_stock":return"En stock";default:return"Inconnu"}},Z=e=>{e?(f(e),E({nom:e.nom,description:e.description,prixAchatCDF:e.prixAchatCDF||0,prixCDF:e.prixCDF,categorie:e.categorie,stock:e.stock,stockMin:e.stockMin,quantiteEnStock:e.quantiteEnStock||e.stock,coutAchatStockCDF:e.coutAchatStockCDF||e.prixAchatCDF*e.stock,prixParPieceCDF:e.prixParPieceCDF||e.prixCDF})):(f(null),E({nom:"",description:"",prixAchatCDF:0,prixCDF:0,categorie:"",stock:0,stockMin:0,quantiteEnStock:0,coutAchatStockCDF:0,prixParPieceCDF:0})),C(!0),T(""),R(""),setTimeout(()=>{document.querySelectorAll('div[role="dialog"] input, div[role="dialog"] textarea').forEach(e=>{const t=e;t.style.pointerEvents="auto",t.style.userSelect="text",(!t.hasAttribute("tabindex")||t.tabIndex<0)&&(t.tabIndex=0)})},100)},ie=()=>{C(!1),f(null),T(""),R("")},re=e.length,se=e.filter(e=>"in_stock"===$i(e)).length,oe=e.filter(e=>"low_stock"===$i(e)).length,le=e.reduce((e,t)=>e+t.prixCDF*t.stock,0);return a.jsxs(i,{children:[a.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[a.jsx(o,{variant:"h4",children:"Inventaire"}),X.canManageProducts&&a.jsx(I,{variant:"contained",startIcon:a.jsx(Pe,{}),onClick:()=>Z(),children:"Nouveau Produit"})]}),k&&a.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>R(""),children:k}),P&&a.jsx(O,{severity:"error",sx:{mb:2},action:a.jsx(I,{color:"inherit",size:"small",onClick:()=>{window.confirm("Cela va supprimer toutes les données et réinitialiser l'application. Continuer?")&&(localStorage.clear(),window.location.reload())},children:"Réinitialiser les données"}),children:P}),a.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Produits"}),a.jsx(o,{variant:"h6",children:re})]}),a.jsx(D,{color:"primary",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"En Stock"}),a.jsx(o,{variant:"h6",color:"success.main",children:se})]}),a.jsx(_,{color:"success",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Stock Bas"}),a.jsx(o,{variant:"h6",color:"warning.main",children:oe})]}),a.jsx(z,{color:"warning",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Valeur Inventaire"}),a.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:[_i(le,B.tauxChangeUSDCDF).primaryAmount," ",_i(le,B.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",_i(le,B.tauxChangeUSDCDF).secondaryAmount]})]}),a.jsx(D,{color:"info",sx:{fontSize:40}})]})})})})]}),a.jsx(c,{sx:{p:2,mb:3},children:a.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[a.jsx(ce,{item:!0,xs:12,md:4,children:a.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par nom, Code QR ou description...",value:d,onChange:e=>u(e.target.value),InputProps:{startAdornment:a.jsx(ae,{position:"start",children:a.jsx(Fe,{})})}})}),a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Catégorie"}),a.jsxs(Se,{value:m,label:"Catégorie",onChange:e=>h(e.target.value),children:[a.jsx(J,{value:"",children:"Toutes les catégories"}),n.map(e=>a.jsx(J,{value:e.nom,children:e.nom},e.id))]})]})}),a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Statut Stock"}),a.jsxs(Se,{value:p,label:"Statut Stock",onChange:e=>x(e.target.value),children:[a.jsx(J,{value:"all",children:"Tous"}),a.jsx(J,{value:"in_stock",children:"En stock"}),a.jsx(J,{value:"low_stock",children:"Stock bas"}),a.jsx(J,{value:"out_of_stock",children:"Rupture"})]})]})}),a.jsx(ce,{item:!0,xs:12,md:2,children:a.jsxs(i,{display:"flex",gap:1,children:[a.jsx(F,{title:"Exporter CSV",children:a.jsx(U,{onClick:()=>{const e=`SmartBoutique_Produits_${(new Date).toISOString().split("T")[0]}.csv`;Oa.downloadCSV(s,Va,e),R("Produits exportés en CSV avec succès (compatible Excel)"),setTimeout(()=>R(""),3e3)},children:a.jsx(Ue,{})})}),a.jsx(F,{title:"Importer CSV",children:a.jsx(U,{onClick:()=>N(!0),children:a.jsx(Te,{})})})]})})]})}),a.jsxs(me,{component:c,children:[a.jsxs(he,{children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:"Produit"}),a.jsx(ge,{children:"Code QR"}),a.jsx(ge,{children:"Catégorie"}),a.jsx(ge,{align:"right",children:"Prix de Vente"}),a.jsx(ge,{align:"center",children:"Stock"}),a.jsx(ge,{align:"center",children:"Qté Stock"}),a.jsx(ge,{align:"right",children:"Coût Stock"}),a.jsx(ge,{align:"center",children:"Statut"}),a.jsx(ge,{children:"Dernière Modif."}),X.canManageProducts&&a.jsx(ge,{align:"center",children:"Actions"})]})}),a.jsx(ye,{children:(-1===j?s:s.slice(g*j,g*j+j)).map(n=>{const r=$i(n);return a.jsxs(xe,{hover:!0,onClick:()=>Z(n),sx:{cursor:"pointer"},children:[a.jsx(ge,{children:a.jsxs(i,{children:[a.jsx(o,{variant:"subtitle2",children:n.nom}),a.jsx(o,{variant:"caption",color:"text.secondary",children:n.description})]})}),a.jsx(ge,{children:a.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[n.codeQR,a.jsx(F,{title:"Code QR",children:a.jsx(U,{size:"small",children:a.jsx(ke,{fontSize:"small"})})})]})}),a.jsx(ge,{children:n.categorie}),a.jsx(ge,{align:"right",children:a.jsxs(i,{children:[a.jsxs(o,{variant:"body2",fontWeight:"medium",children:[_i(n.prixCDF,B.tauxChangeUSDCDF).primaryAmount," ",_i(n.prixCDF,B.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_i(n.prixCDF,B.tauxChangeUSDCDF).secondaryAmount]})]})}),a.jsx(ge,{align:"center",children:a.jsxs(i,{children:[a.jsx(o,{variant:"body2",children:n.stock}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["Min: ",n.stockMin]})]})}),a.jsx(ge,{align:"center",children:a.jsxs(i,{children:[a.jsx(o,{variant:"body2",fontWeight:"medium",children:n.quantiteEnStock||n.stock}),a.jsx(o,{variant:"caption",color:"text.secondary",children:"Réel"})]})}),a.jsx(ge,{align:"right",children:a.jsxs(i,{children:[a.jsx(o,{variant:"body2",fontWeight:"medium",children:n.coutAchatStockCDF?`${_i(n.coutAchatStockCDF,B.tauxChangeUSDCDF).primaryAmount} ${_i(n.coutAchatStockCDF,B.tauxChangeUSDCDF).primaryCurrency}`:`${_i(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).primaryAmount} ${_i(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).primaryCurrency}`}),a.jsx(o,{variant:"caption",color:"text.secondary",children:n.coutAchatStockCDF?`≈ $${_i(n.coutAchatStockCDF,B.tauxChangeUSDCDF).secondaryAmount} • Total`:`≈ $${_i(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).secondaryAmount} • Total`})]})}),a.jsx(ge,{align:"center",children:a.jsx(A,{icon:Y(r),label:K(r),color:G(r),size:"small"})}),a.jsx(ge,{children:(()=>{try{const e=new Date(n.dateModification);return isNaN(e.getTime())?"Date invalide":Ut(e,"dd/MM/yyyy",{locale:Di})}catch(e){return"Date invalide"}})()}),X.canManageProducts&&a.jsx(ge,{align:"center",children:a.jsxs(i,{display:"flex",gap:1,children:[a.jsx(F,{title:"Modifier",children:a.jsx(U,{size:"small",onClick:()=>Z(n),children:a.jsx(Re,{fontSize:"small"})})}),hi.hasRole(["super_admin"])&&a.jsx(F,{title:"Supprimer",children:a.jsx(U,{size:"small",color:"error",onClick:()=>(async n=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer le produit "${n.nom}" ?`)){const a=e.filter(e=>e.id!==n.id);t(a),await mi.setProducts(a),R("Produit supprimé avec succès"),setTimeout(()=>R(""),3e3)}})(n),children:a.jsx(q,{fontSize:"small"})})})]})})]},n.id)})})]}),a.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:s.length,rowsPerPage:-1===j?s.length:j,page:-1===j?0:g,onPageChange:(e,t)=>{-1!==j&&y(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);S(t),y(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===j?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),a.jsxs(Ae,{open:v,onClose:ie,maxWidth:"md",fullWidth:!0,children:[a.jsx(Ie,{children:b?"Modifier le Produit":"Nouveau Produit"}),a.jsxs(Ne,{children:[P&&a.jsx(O,{severity:"error",sx:{mb:2},children:P}),k&&a.jsx(O,{severity:"success",sx:{mb:2},children:k}),a.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Nom du produit *",value:w.nom,onChange:e=>E({...w,nom:e.target.value})})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Description",multiline:!0,rows:3,value:w.description,onChange:e=>E({...w,description:e.target.value})})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(Yi,{label:"Prix d'achat *",value:w.prixAchatCDF,onChange:e=>E({...w,prixAchatCDF:e}),min:0,max:1e7,step:100,exchangeRate:B.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,error:w.prixAchatCDF<=0,helperText:w.prixAchatCDF<=0?"Le prix d'achat doit être supérieur à zéro":"Prix d'achat du produit en CDF"})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(Yi,{label:"Prix de vente *",value:w.prixCDF,onChange:e=>E({...w,prixCDF:e}),min:0,max:1e7,step:100,exchangeRate:B.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,error:w.prixCDF<=w.prixAchatCDF,helperText:w.prixCDF<=w.prixAchatCDF?"Le prix de vente doit être supérieur au prix d'achat pour générer un bénéfice":"Prix de vente du produit en CDF"})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Catégorie *"}),a.jsx(Se,{value:w.categorie,label:"Catégorie *",onChange:e=>E({...w,categorie:e.target.value}),children:n.map(e=>a.jsx(J,{value:e.nom,children:e.nom},e.id))})]})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(Ki,{value:w.stock,onChange:e=>E({...w,stock:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Stock actuel",helperText:"Quantité actuelle en stock"})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(Ki,{value:w.stockMin,onChange:e=>E({...w,stockMin:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Stock minimum",helperText:"Seuil d'alerte pour stock bas"})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(o,{variant:"h6",sx:{mt:2,mb:1,color:"primary.main"},children:"Gestion Avancée de l'Inventaire"})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(Ki,{value:w.quantiteEnStock,onChange:e=>E({...w,quantiteEnStock:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Quantité en Stock",helperText:"Quantité réelle en stock (éditable)"})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(Yi,{label:"Coût d'Achat du Stock",value:w.coutAchatStockCDF,onChange:e=>E({...w,coutAchatStockCDF:e}),min:0,max:5e7,step:100,exchangeRate:B.tauxChangeUSDCDF,showSlider:!0,allowUSDInput:!0,helperText:"Coût total d'achat du stock actuel"})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(Yi,{label:"Prix par Pièce",value:w.prixParPieceCDF,onChange:e=>E({...w,prixParPieceCDF:e}),min:0,max:1e7,step:100,exchangeRate:B.tauxChangeUSDCDF,showSlider:!0,allowUSDInput:!0,helperText:"Prix unitaire pour vente au détail"})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(o,{variant:"h6",sx:{mt:2,mb:1,color:"primary.main"},children:"Valeur du Stock avec Bénéfice"})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Valeur Stock avec Bénéfice (CDF)",value:(()=>{const e=(w.quantiteEnStock||0)*(w.prixParPieceCDF||w.prixCDF||0);return Bi(e,"CDF")})(),InputProps:{readOnly:!0,style:{backgroundColor:"#f5f5f5",fontWeight:"bold",color:"#1976d2"}},helperText:"Valeur totale du stock actuel avec marge bénéficiaire incluse",variant:"outlined"})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Valeur Stock avec Bénéfice (USD)",value:(()=>{const e=(w.quantiteEnStock||0)*(w.prixParPieceCDF||w.prixCDF||0)/(B.tauxChangeUSDCDF||2800);return Bi(e,"USD")})(),InputProps:{readOnly:!0,style:{backgroundColor:"#f5f5f5",fontWeight:"bold",color:"#1976d2"}},helperText:"Équivalent en USD de la valeur du stock avec bénéfice",variant:"outlined"})})]})]}),a.jsxs(Le,{children:[a.jsx(I,{onClick:ie,children:"Annuler"}),a.jsx(I,{onClick:async()=>{if(!w.nom.trim())return void T("Le nom du produit est requis");if(w.prixAchatCDF<=0)return void T("Le prix d'achat doit être supérieur à zéro");if(w.prixCDF<=0)return void T("Le prix de vente doit être supérieur à zéro");if(w.stock<0)return void T("Le stock ne peut pas être négatif");if(w.stockMin<0)return void T("Le stock minimum ne peut pas être négatif");if(!w.categorie)return void T("La catégorie est requise");const n=Oi(w.prixAchatCDF,w.prixCDF);if(!n.isValid)return void T(n.errorMessage||"Erreur de validation des prix");const a=await mi.getSettings(),i=(new Date).toISOString(),r=Li(w.prixAchatCDF,w.prixCDF,a.tauxChangeUSDCDF);if(b){const n={...b,nom:w.nom.trim(),description:w.description.trim(),prixAchatCDF:w.prixAchatCDF,prixAchatUSD:Ji(w.prixAchatCDF,a.tauxChangeUSDCDF),prixCDF:w.prixCDF,prixUSD:Ji(w.prixCDF,a.tauxChangeUSDCDF),beneficeUnitaireCDF:r.beneficeUnitaireCDF,beneficeUnitaireUSD:r.beneficeUnitaireUSD,categorie:w.categorie,stock:w.stock,stockMin:w.stockMin,dateModification:i,quantiteEnStock:w.quantiteEnStock,coutAchatStockCDF:w.coutAchatStockCDF,coutAchatStockUSD:Ji(w.coutAchatStockCDF,a.tauxChangeUSDCDF),prixParPieceCDF:w.prixParPieceCDF,prixParPieceUSD:Ji(w.prixParPieceCDF,a.tauxChangeUSDCDF)},s=e.map(e=>e.id===b.id?n:e);t(s),await mi.setProducts(s),R("Produit mis à jour avec succès")}else{const n={id:Date.now().toString(),nom:w.nom.trim(),description:w.description.trim(),prixAchatCDF:w.prixAchatCDF,prixAchatUSD:Ji(w.prixAchatCDF,a.tauxChangeUSDCDF),prixCDF:w.prixCDF,prixUSD:Ji(w.prixCDF,a.tauxChangeUSDCDF),beneficeUnitaireCDF:r.beneficeUnitaireCDF,beneficeUnitaireUSD:r.beneficeUnitaireUSD,codeQR:Wi(),categorie:w.categorie,stock:w.stock,stockMin:w.stockMin,codeBarres:zi(),dateCreation:i,dateModification:i,quantiteEnStock:w.quantiteEnStock,coutAchatStockCDF:w.coutAchatStockCDF,coutAchatStockUSD:Ji(w.coutAchatStockCDF,a.tauxChangeUSDCDF),prixParPieceCDF:w.prixParPieceCDF,prixParPieceUSD:Ji(w.prixParPieceCDF,a.tauxChangeUSDCDF)},s=[...e,n];t(s),await mi.setProducts(s),R("Produit créé avec succès")}setTimeout(()=>{ie()},1500)},variant:"contained",children:b?"Mettre à jour":"Créer"})]})]}),a.jsxs(Ae,{open:M,onClose:()=>N(!1),maxWidth:"md",fullWidth:!0,children:[a.jsx(Ie,{children:"Importer des Produits depuis CSV"}),a.jsxs(Ne,{children:[a.jsx(Oe,{sx:{mb:2},children:"Collez le contenu CSV des produits à importer. Format attendu: ID, Nom du Produit, Description, Prix CDF, Prix USD, Code QR, Catégorie, Stock, Stock Minimum, Code Barres, Date de Création, Date de Modification"}),a.jsx(ne,{fullWidth:!0,multiline:!0,rows:10,value:L,onChange:e=>V(e.target.value),placeholder:"ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification\n1,iPhone 15,Smartphone Apple,2240000,800,SB123,Électronique,25,5,1234567890123,2024-01-01,2024-01-01",variant:"outlined"})]}),a.jsxs(Le,{children:[a.jsx(I,{onClick:()=>N(!1),children:"Annuler"}),a.jsx(I,{onClick:async()=>{if(L.trim())try{const n=Oa.csvToArray(L,Va),a=Oa.validateCSVData(n,Va);if(!a.isValid)return void T("Données CSV invalides: "+a.errors.join(", "));const i=n.map((e,t)=>({...e,id:e.id||Date.now().toString()+t,dateCreation:e.dateCreation||(new Date).toISOString(),dateModification:e.dateModification||(new Date).toISOString(),codeQR:e.codeQR||Wi(),codeBarres:e.codeBarres||zi()})),r=new Set(e.map(e=>e.id)),s=i.filter(e=>!r.has(e.id)),o=[...e,...s];t(o),await mi.setProducts(o),R(`${s.length} produits importés avec succès`),N(!1),V(""),setTimeout(()=>R(""),3e3)}catch(n){T("Erreur lors de l'importation: "+n.message)}else T("Veuillez saisir le contenu CSV à importer")},variant:"contained",children:"Importer"})]})]})]})};class er{static async generateSalesReceiptNumber(){const e=(new Date).toISOString().slice(0,10).replace(/-/g,""),t=((await mi.getSales()).filter(t=>{var n;return new Date(t.datevente).toISOString().slice(0,10).replace(/-/g,"")===e&&(null==(n=t.numeroRecu)?void 0:n.startsWith(`RV-${e}`))}).length+1).toString().padStart(4,"0");return`RV-${e}-${t}`}static async generateExpenseReceiptNumber(){const e=(new Date).toISOString().slice(0,10).replace(/-/g,""),t=((await mi.getExpenses()).filter(t=>{var n;return new Date(t.dateDepense).toISOString().slice(0,10).replace(/-/g,"")===e&&(null==(n=t.numeroRecu)?void 0:n.startsWith(`RD-${e}`))}).length+1).toString().padStart(4,"0");return`RD-${e}-${t}`}static async createSalesReceiptData(e){const t=await mi.getSettings();return{type:"sale",numero:e.numeroRecu||await this.generateSalesReceiptNumber(),date:e.datevente,entreprise:t.entreprise,sale:e,vendeur:e.vendeur}}static async createExpenseReceiptData(e){const t=await mi.getSettings();return{type:"expense",numero:e.numeroRecu||await this.generateExpenseReceiptNumber(),date:e.dateDepense,entreprise:t.entreprise,expense:e,creePar:e.creePar}}static getPaymentMethodLabel(e){switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}}static formatCurrency(e,t){const{formatCurrency:n}=require("@/utils/currencyUtils.js");return n(e,t)}static formatReceiptDate(e){return new Date(e).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}static async printReceipt(){return new Promise(e=>{try{if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)try{const{ipcRenderer:t}=window.require("electron"),n={silent:!1,printBackground:!0,color:!1,margin:{marginType:"none"},landscape:!1,pagesPerSheet:1,collate:!1,copies:1,header:"",footer:""};t.invoke("print-receipt",n).then(()=>{console.log("Receipt printed successfully via Electron IPC"),e()}).catch(t=>{console.error("Electron IPC print failed:",t),window.print(),e()})}catch(t){console.error("IPC not available, falling back to window.print():",t),window.print(),e()}else window.print(),e()}catch(fr){console.error("Error during printing:",fr),window.print(),e()}})}static async shouldAutoPrint(){var e;return(null==(e=(await mi.getSettings()).impression)?void 0:e.impressionAutomatique)||!1}static async getPaperSize(){var e;return(null==(e=(await mi.getSettings()).impression)?void 0:e.taillePapier)||"thermal"}}const tr=({receiptData:e,paperSize:t="thermal"})=>{const{sale:n,entreprise:r,numero:s,vendeur:l}=e;return a.jsxs(c,{className:"receipt-container "+("thermal"===t?"thermal-receipt":"a4-receipt"),elevation:0,sx:{p:2,fontFamily:"monospace",fontSize:"thermal"===t?"12px":"14px",lineHeight:1.4,maxWidth:"thermal"===t?"80mm":"210mm",margin:"0 auto",backgroundColor:"white",color:"black","@media print":{backgroundColor:"white !important",color:"black !important",boxShadow:"none !important",border:"none !important"}},children:[a.jsxs(i,{textAlign:"center",mb:2,children:[r.logo&&a.jsx(i,{mb:1,display:"flex",justifyContent:"center",alignItems:"center",children:a.jsx(i,{component:"img",src:r.logo,alt:`${r.nom} Logo`,sx:{maxWidth:"thermal"===t?"70mm":"150px",maxHeight:"60px",objectFit:"contain",mb:1}})}),a.jsx(o,{variant:"h6",fontWeight:"bold",sx:{fontSize:"16px"},children:r.nom}),a.jsx(o,{variant:"body2",sx:{fontSize:"12px"},children:r.adresse}),a.jsxs(o,{variant:"body2",sx:{fontSize:"12px"},children:["Tél: ",r.telephone]}),r.rccm&&a.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["RCCM: ",r.rccm]}),r.idNat&&a.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["ID NAT: ",r.idNat]})]}),a.jsx(h,{sx:{my:1}}),a.jsxs(i,{textAlign:"center",mb:2,children:[a.jsxs(o,{variant:"h6",fontWeight:"bold",children:["REÇU DE VENTE #",s]}),a.jsx(o,{variant:"body2",children:er.formatReceiptDate(n.datevente)})]}),a.jsx(i,{mb:1,children:a.jsxs(o,{variant:"body2",children:[a.jsx("strong",{children:"Vendeur:"})," ",l]})}),a.jsxs(i,{mb:2,children:[a.jsxs(o,{variant:"body2",children:[a.jsx("strong",{children:"Client:"})," ",n.nomClient]}),n.telephoneClient&&a.jsxs(o,{variant:"body2",children:[a.jsx("strong",{children:"Téléphone:"})," ",n.telephoneClient]}),n.adresseClient&&a.jsxs(o,{variant:"body2",children:[a.jsx("strong",{children:"Adresse:"})," ",n.adresseClient]})]}),a.jsx(h,{sx:{my:1}}),a.jsx(me,{children:a.jsxs(he,{size:"small",sx:{"& .MuiTableCell-root":{padding:"4px",border:"none"}},children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:a.jsx("strong",{children:"Produit"})}),a.jsx(ge,{align:"center",children:a.jsx("strong",{children:"Qté"})}),a.jsx(ge,{align:"right",children:a.jsx("strong",{children:"Prix unit."})}),a.jsx(ge,{align:"right",children:a.jsx("strong",{children:"Sous-total"})})]})}),a.jsx(ye,{children:n.produits.map((e,t)=>a.jsxs(xe,{children:[a.jsx(ge,{sx:{fontSize:"11px"},children:e.nomProduit}),a.jsx(ge,{align:"center",sx:{fontSize:"11px"},children:e.quantite}),a.jsx(ge,{align:"right",sx:{fontSize:"11px"},children:er.formatCurrency(e.prixUnitaireCDF,"CDF")}),a.jsx(ge,{align:"right",sx:{fontSize:"11px"},children:er.formatCurrency(e.totalCDF,"CDF")})]},t))})]})}),a.jsx(h,{sx:{my:1}}),a.jsx(i,{mb:2,children:a.jsxs(o,{variant:"body2",children:[a.jsx("strong",{children:"Méthode de paiement:"})," ",er.getPaymentMethodLabel(n.methodePaiement)]})}),a.jsxs(i,{mb:2,children:[a.jsxs(i,{display:"flex",justifyContent:"space-between",children:[a.jsx(o,{variant:"body2",children:"Sous-total:"}),a.jsx(o,{variant:"body2",children:er.formatCurrency(n.totalCDF,"CDF")})]}),a.jsxs(i,{display:"flex",justifyContent:"space-between",children:[a.jsx(o,{variant:"body1",fontWeight:"bold",children:"Total CDF:"}),a.jsx(o,{variant:"body1",fontWeight:"bold",children:er.formatCurrency(n.totalCDF,"CDF")})]}),n.totalUSD&&a.jsxs(i,{display:"flex",justifyContent:"space-between",children:[a.jsx(o,{variant:"body2",children:"Total USD:"}),a.jsxs(o,{variant:"body2",children:["≈ ",er.formatCurrency(n.totalUSD,"USD")]})]})]}),a.jsx(h,{sx:{my:1}}),n.notes&&a.jsx(i,{mb:2,children:a.jsxs(o,{variant:"body2",children:[a.jsx("strong",{children:"Notes:"})," ",n.notes]})}),a.jsxs(i,{textAlign:"center",mt:2,children:[a.jsx(o,{variant:"body2",fontWeight:"bold",children:"Merci pour votre achat!"}),a.jsxs(o,{variant:"caption",sx:{fontSize:"10px"},children:["Reçu ID: ",s]})]})]})},nr=({receiptData:e,paperSize:t="thermal"})=>{const{expense:n,entreprise:r,numero:s,creePar:l}=e;return a.jsxs(c,{className:"receipt-container "+("thermal"===t?"thermal-receipt":"a4-receipt"),elevation:0,sx:{p:2,fontFamily:"monospace",fontSize:"thermal"===t?"12px":"14px",lineHeight:1.4,maxWidth:"thermal"===t?"80mm":"210mm",margin:"0 auto",backgroundColor:"white",color:"black","@media print":{backgroundColor:"white !important",color:"black !important",boxShadow:"none !important",border:"none !important"}},children:[a.jsxs(i,{textAlign:"center",mb:2,children:[r.logo&&a.jsx(i,{mb:1,display:"flex",justifyContent:"center",alignItems:"center",children:a.jsx(i,{component:"img",src:r.logo,alt:`${r.nom} Logo`,sx:{maxWidth:"thermal"===t?"70mm":"150px",maxHeight:"60px",objectFit:"contain",mb:1}})}),a.jsx(o,{variant:"h6",fontWeight:"bold",sx:{fontSize:"16px"},children:r.nom}),a.jsx(o,{variant:"body2",sx:{fontSize:"12px"},children:r.adresse}),a.jsxs(o,{variant:"body2",sx:{fontSize:"12px"},children:["Tél: ",r.telephone]}),r.rccm&&a.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["RCCM: ",r.rccm]}),r.idNat&&a.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["ID NAT: ",r.idNat]})]}),a.jsx(h,{sx:{my:1}}),a.jsxs(i,{textAlign:"center",mb:2,children:[a.jsxs(o,{variant:"h6",fontWeight:"bold",children:["REÇU DE DÉPENSE #",s]}),a.jsx(o,{variant:"body2",children:er.formatReceiptDate(n.dateDepense)})]}),a.jsx(i,{mb:1,children:a.jsxs(o,{variant:"body2",children:[a.jsx("strong",{children:"Créé par:"})," ",l]})}),a.jsx(h,{sx:{my:1}}),a.jsxs(i,{mb:2,children:[a.jsx(o,{variant:"body2",mb:1,children:a.jsx("strong",{children:"Description:"})}),a.jsx(o,{variant:"body2",sx:{pl:1,mb:2},children:n.description}),a.jsxs(o,{variant:"body2",children:[a.jsx("strong",{children:"Catégorie:"})," ",n.categorie]})]}),a.jsx(h,{sx:{my:1}}),a.jsxs(i,{mb:2,children:[a.jsxs(i,{display:"flex",justifyContent:"space-between",mb:1,children:[a.jsx(o,{variant:"body1",fontWeight:"bold",children:"Montant:"}),a.jsx(o,{variant:"body1",fontWeight:"bold",children:er.formatCurrency(n.montantCDF,"CDF")})]}),n.montantUSD&&a.jsxs(i,{display:"flex",justifyContent:"space-between",children:[a.jsx(o,{variant:"body2",children:"Équivalent USD:"}),a.jsxs(o,{variant:"body2",children:["≈ ",er.formatCurrency(n.montantUSD,"USD")]})]})]}),a.jsx(h,{sx:{my:1}}),n.notes&&a.jsxs(i,{mb:2,children:[a.jsx(o,{variant:"body2",mb:1,children:a.jsx("strong",{children:"Notes:"})}),a.jsx(o,{variant:"body2",sx:{pl:1},children:n.notes})]}),a.jsxs(i,{textAlign:"center",mt:2,children:[a.jsx(o,{variant:"body2",fontWeight:"bold",children:"Reçu de dépense validé"}),a.jsxs(o,{variant:"caption",sx:{fontSize:"10px"},children:["Reçu ID: ",s]})]})]})},ar=({open:e,onClose:t,receiptData:n,paperSize:r="thermal",onPrintSuccess:s})=>{const[l,c]=Ct.useState(!1);return n?a.jsxs(Ae,{open:e,onClose:t,maxWidth:"md",fullWidth:!0,className:"receipt-preview-modal",PaperProps:{sx:{maxHeight:"90vh","@media print":{boxShadow:"none",margin:0,maxWidth:"none",maxHeight:"none"}}},children:[a.jsxs(Ie,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center","@media print":{display:"none"}},children:[a.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[a.jsx(se,{}),a.jsx(o,{variant:"h6",children:"Aperçu du reçu"})]}),a.jsx(U,{onClick:t,size:"small",children:a.jsx(Ve,{})})]}),a.jsx(Ne,{sx:{p:0,"@media print":{padding:0,margin:0}},children:a.jsx(i,{sx:{p:2,"@media print":{padding:0,margin:0}},children:"sale"===n.type?a.jsx(tr,{receiptData:n,paperSize:r}):a.jsx(nr,{receiptData:n,paperSize:r})})}),a.jsxs(Le,{sx:{p:2,gap:1,"@media print":{display:"none"}},children:[a.jsx(I,{onClick:t,variant:"outlined",disabled:l,children:"Fermer"}),a.jsx(I,{onClick:async()=>{if(n){c(!0);try{const e=document.createElement("div");e.className="receipt-print-container",e.style.cssText="\n        position: fixed;\n        top: -9999px;\n        left: -9999px;\n        width: 100%;\n        height: auto;\n        background: white;\n        z-index: 9999;\n      ";const n=document.querySelector(".receipt-container");if(n){const t=n.cloneNode(!0);t.style.cssText="\n          display: block !important;\n          position: static !important;\n          width: 100% !important;\n          height: auto !important;\n          margin: 0 !important;\n          padding: 5mm !important;\n          background: white !important;\n          color: black !important;\n        ",e.appendChild(t)}document.body.appendChild(e),await new Promise(e=>setTimeout(e,200));const a=document.querySelector(".receipt-preview-modal");a&&(a.style.display="none"),await er.printReceipt(),document.body.removeChild(e),a&&(a.style.display="block"),s&&s(),setTimeout(()=>{t()},500)}catch(fr){console.error("Error printing receipt:",fr)}finally{c(!1)}}},variant:"contained",startIcon:l?a.jsx(qe,{size:16}):a.jsx(Be,{}),disabled:l,children:l?"Impression...":"Imprimer"})]})]}):null},ir=()=>{const[e,t]=Ct.useState([]),[n,r]=Ct.useState([]),[s,l]=Ct.useState([]),[d,u]=Ct.useState(""),[m,g]=Ct.useState(0),[j,D]=Ct.useState(10),[C,b]=Ct.useState(!1),[w,E]=Ct.useState(!1),[P,T]=Ct.useState(null),[k,R]=Ct.useState(""),[M,N]=Ct.useState(""),[L,q]=Ct.useState([]),[B,_]=Ct.useState(null),[$,z]=Ct.useState(1),[W,X]=Ct.useState(""),[Q,H]=Ct.useState(""),[G,Y]=Ct.useState(""),[K,Z]=Ct.useState("cash"),[ie,re]=Ct.useState("cash"),[se,oe]=Ct.useState("CDF"),[le,de]=Ct.useState(""),[ue,ve]=Ct.useState(""),[be,fe]=Ct.useState(!1),[we,Ee]=Ct.useState({tauxChangeUSDCDF:2800}),[Ue,Te]=Ct.useState(!1),[ke,Re]=Ct.useState(null),[Oe,Ve]=Ct.useState(!1),Be=hi.getUserPermissions(),Ge=hi.getCurrentUser(),Ye=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(t){return console.warn("Error parsing date:",e,t),null}};Ct.useEffect(()=>{Ke()},[]),Ct.useEffect(()=>{Ze()},[e,d]),Ct.useEffect(()=>{-1===j&&D(s.length||1)},[s.length,j]);const Ke=async()=>{const e=await mi.getSales(),n=await mi.getProducts(),a=await mi.getSettings();t(e),r(n),Ee(a)},Ze=()=>{let t=e;d&&(t=t.filter(e=>e.nomClient.toLowerCase().includes(d.toLowerCase())||e.id.toLowerCase().includes(d.toLowerCase())||e.vendeur.toLowerCase().includes(d.toLowerCase()))),l(t)},et=()=>{b(!1),tt(),R(""),N("")},tt=()=>{q([]),_(null),z(1),X(""),H(""),Y(""),Z("cash"),re("cash"),oe("CDF"),de(""),ve(""),fe(!1)},nt=e=>{const t=L.filter((t,n)=>n!==e);q(t)},at=()=>L.reduce((e,t)=>({CDF:Ui(e.CDF+t.totalCDF),USD:Ui(e.USD+(t.totalUSD||0))}),{CDF:0,USD:0}),it=e=>"USD"===se?{price:e.prixUSD||e.prixCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),currency:"USD",symbol:"$"}:{price:e.prixCDF,currency:"CDF",symbol:""},rt=e=>{const t=it(e);return`${t.symbol}${t.price.toLocaleString("fr-FR",{minimumFractionDigits:"USD"===t.currency?2:0,maximumFractionDigits:"USD"===t.currency?2:0})} ${t.currency}`},st=e=>{T(e),E(!0)},ot=e=>{switch(e){case"cash":default:return a.jsx(f,{});case"banque":return a.jsx($e,{});case"mobile_money":return a.jsx(He,{})}},lt=e=>{switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}},ct=e.filter(e=>{const t=Ye(e.datevente);if(!t)return!1;const n=new Date;return t.toDateString()===n.toDateString()}),dt=ct.reduce((e,t)=>e+t.totalCDF,0),ut=e.length,mt=e.reduce((e,t)=>e+t.totalCDF,0),ht=ut>0?mt/ut:0;return a.jsxs(i,{children:[a.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[a.jsx(o,{variant:"h4",children:"Gestion des Ventes"}),Be.canManageSales&&a.jsx(I,{variant:"contained",startIcon:a.jsx(Pe,{}),onClick:()=>{tt(),b(!0),R(""),N("")},children:"Nouvelle Vente"})]}),M&&a.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>N(""),children:M}),a.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes du jour"}),a.jsx(o,{variant:"h6",children:ct.length}),a.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[_i(dt,we.tauxChangeUSDCDF).primaryAmount," ",_i(dt,we.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_i(dt,we.tauxChangeUSDCDF).secondaryAmount]})]}),a.jsx(S,{color:"primary",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Ventes"}),a.jsx(o,{variant:"h6",children:ut}),a.jsxs(o,{variant:"body2",color:"success.main",fontWeight:"medium",children:[_i(mt,we.tauxChangeUSDCDF).primaryAmount," ",_i(mt,we.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_i(mt,we.tauxChangeUSDCDF).secondaryAmount]})]}),a.jsx(_e,{color:"success",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Vente Moyenne"}),a.jsxs(o,{variant:"h6",fontWeight:"medium",children:[_i(ht,we.tauxChangeUSDCDF).primaryAmount," ",_i(ht,we.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_i(ht,we.tauxChangeUSDCDF).secondaryAmount]})]}),a.jsx(v,{color:"info",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes à Crédit"}),a.jsx(o,{variant:"h6",children:e.filter(e=>"credit"===e.typeVente).length})]}),a.jsx($e,{color:"warning",sx:{fontSize:40}})]})})})})]}),a.jsx(c,{sx:{p:2,mb:3},children:a.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par client, ID vente ou vendeur...",value:d,onChange:e=>u(e.target.value),InputProps:{startAdornment:a.jsx(ae,{position:"start",children:a.jsx(Fe,{})})}})}),a.jsxs(me,{component:c,children:[a.jsxs(he,{children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:"Produits Vendus"}),a.jsx(ge,{children:"Client"}),a.jsx(ge,{children:"Vendeur"}),a.jsx(ge,{align:"right",children:"Total CDF"}),a.jsx(ge,{align:"right",children:"Total USD"}),a.jsx(ge,{align:"center",children:"Paiement"}),a.jsx(ge,{align:"center",children:"Type"}),a.jsx(ge,{children:"Date"}),a.jsx(ge,{align:"center",children:"Actions"})]})}),a.jsx(ye,{children:(-1===j?s:s.slice(m*j,m*j+j)).map(e=>a.jsxs(xe,{hover:!0,onClick:()=>st(e),sx:{cursor:"pointer"},children:[a.jsx(ge,{children:a.jsxs(i,{children:[a.jsx(o,{variant:"subtitle2",color:"primary",gutterBottom:!0,children:e.produits.map(e=>e.nomProduit).join(", ")}),a.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",children:["ID: ",e.id," • ",e.produits.length," article",e.produits.length>1?"s":""]})]})}),a.jsx(ge,{children:a.jsxs(i,{children:[a.jsx(o,{variant:"subtitle2",children:e.nomClient}),e.telephoneClient&&a.jsx(o,{variant:"caption",color:"text.secondary",children:e.telephoneClient})]})}),a.jsx(ge,{children:e.vendeur}),a.jsx(ge,{align:"right",children:Bi(e.totalCDF,"CDF")}),a.jsx(ge,{align:"right",children:e.totalUSD?Bi(e.totalUSD,"USD"):"-"}),a.jsx(ge,{align:"center",children:a.jsx(A,{icon:ot(e.methodePaiement),label:lt(e.methodePaiement),size:"small"})}),a.jsx(ge,{align:"center",children:a.jsx(A,{label:"cash"===e.typeVente?"Cash":"Crédit",color:"cash"===e.typeVente?"success":"warning",size:"small"})}),a.jsx(ge,{children:(()=>{const t=Ye(e.datevente);return t?Ut(t,"dd/MM/yyyy HH:mm",{locale:Di}):"Date invalide"})()}),a.jsx(ge,{align:"center",children:a.jsx(F,{title:"Voir détails",children:a.jsx(U,{size:"small",onClick:()=>st(e),children:a.jsx(f,{fontSize:"small"})})})})]},e.id))})]}),a.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:s.length,rowsPerPage:-1===j?s.length:j,page:-1===j?0:m,onPageChange:(e,t)=>{-1!==j&&g(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);D(t),g(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===j?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),a.jsxs(Ae,{open:C,onClose:et,maxWidth:"lg",fullWidth:!0,children:[a.jsx(Ie,{children:"Nouvelle Vente"}),a.jsxs(Ne,{children:[k&&a.jsx(O,{severity:"error",sx:{mb:2},children:k}),M&&a.jsx(O,{severity:"success",sx:{mb:2},children:M}),a.jsxs(i,{sx:{mb:3,p:2,bgcolor:"primary.50",borderRadius:1,border:"1px solid",borderColor:"primary.200"},children:[a.jsx(o,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:"bold",color:"primary.main"},children:"💱 Devise de la Vente"}),a.jsxs(je,{fullWidth:!0,size:"small",children:[a.jsx(De,{children:"Sélectionnez la devise pour cette vente"}),a.jsxs(Se,{value:se,label:"Sélectionnez la devise pour cette vente",onChange:e=>oe(e.target.value),children:[a.jsx(J,{value:"CDF",children:a.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[a.jsx(o,{children:"🇨🇩"}),a.jsx(o,{children:"CDF (Franc Congolais) - Devise principale"})]})}),a.jsx(J,{value:"USD",children:a.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[a.jsx(o,{children:"🇺🇸"}),a.jsx(o,{children:"USD (Dollar Américain)"})]})})]})]}),a.jsx(o,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Les prix des produits seront affichés dans la devise sélectionnée"})]}),a.jsxs(ce,{container:!0,spacing:3,children:[a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"h6",gutterBottom:!0,children:"Sélection des Produits"}),a.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ze,{options:n.filter(e=>e.stock>0),getOptionLabel:e=>`${e.nom} - ${rt(e)} - Stock: ${e.stock}`,value:B,onChange:(e,t)=>_(t),renderInput:e=>a.jsx(ne,{...e,label:"Produit",fullWidth:!0}),renderOption:(e,t)=>a.jsx(i,{component:"li",...e,children:a.jsxs(i,{sx:{display:"flex",flexDirection:"column",width:"100%"},children:[a.jsx(o,{variant:"body1",sx:{fontWeight:500},children:t.nom}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["Prix: ",rt(t)," • Stock: ",t.stock," • Code: ",t.codeQR]})]})})})}),a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsxs(i,{children:[a.jsx(Ki,{value:$,onChange:z,min:1,max:(null==B?void 0:B.stock)||999,size:"small",showButtons:!0,allowDirectInput:!0,label:"Quantité"}),B&&a.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",sx:{mt:1},children:["Stock disponible: ",B.stock]})]})}),a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsx(I,{fullWidth:!0,variant:"contained",onClick:()=>{if(!B)return void R("Veuillez sélectionner un produit");if($<=0)return void R("La quantité doit être supérieure à 0");if($>B.stock)return void R("Quantité insuffisante en stock");const e=L.findIndex(e=>e.produitId===B.id);if(e>=0){const t=[...L],n=t[e].quantite+$;if(n>B.stock)return void R("Quantité totale dépasse le stock disponible");t[e]={...t[e],quantite:n,totalCDF:n*B.prixCDF,totalUSD:B.prixUSD?n*B.prixUSD:void 0},q(t)}else{const e={produitId:B.id,nomProduit:B.nom,quantite:$,prixUnitaireCDF:B.prixCDF,prixUnitaireUSD:B.prixUSD,totalCDF:$*B.prixCDF,totalUSD:B.prixUSD?$*B.prixUSD:void 0};q([...L,e])}_(null),z(1),R("")},disabled:!B,children:"Ajouter"})})]}),B&&a.jsx(i,{sx:{mt:2,p:2,bgcolor:"grey.50",borderRadius:1,border:"1px solid",borderColor:"grey.300"},children:a.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsxs(o,{variant:"subtitle2",sx:{fontWeight:"bold"},children:["Produit sélectionné: ",B.nom]}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["Code: ",B.codeQR," • Catégorie: ",B.categorie]})]}),a.jsxs(ce,{item:!0,xs:12,md:3,children:[a.jsxs(o,{variant:"subtitle2",color:"primary",sx:{fontWeight:"bold"},children:["Prix unitaire: ",rt(B)]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["Devise: ",se]})]}),a.jsxs(ce,{item:!0,xs:12,md:3,children:[a.jsxs(o,{variant:"subtitle2",sx:{fontWeight:"bold"},children:["Total: ",(()=>{const e=it(B),t=e.price*$;return`${e.symbol}${t.toLocaleString("fr-FR",{minimumFractionDigits:"USD"===e.currency?2:0,maximumFractionDigits:"USD"===e.currency?2:0})} ${e.currency}`})()]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:[$," × ",rt(B)]})]})]})})]}),a.jsxs(ce,{item:!0,xs:12,children:[a.jsxs(o,{variant:"h6",gutterBottom:!0,children:["Panier (",L.length," articles) - Devise: ",se]}),0===L.length?a.jsx(O,{severity:"info",children:"Aucun produit ajouté"}):a.jsxs(p,{children:[L.map((e,t)=>{var r;const s=(e=>{if("USD"===se){const t=e.prixUnitaireUSD||e.prixUnitaireCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),n=e.totalUSD||e.totalCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800);return{unitPrice:`$${t.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`,total:`$${n.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`}}return{unitPrice:`${e.prixUnitaireCDF.toLocaleString("fr-FR")} CDF`,total:`${e.totalCDF.toLocaleString("fr-FR")} CDF`}})(e);return a.jsxs(ft.Fragment,{children:[a.jsxs(x,{children:[a.jsx(y,{primary:a.jsx(o,{variant:"subtitle1",sx:{fontWeight:500},children:e.nomProduit}),secondary:`Prix unitaire: ${s.unitPrice}`}),a.jsx(V,{children:a.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[a.jsx(Ki,{value:e.quantite,onChange:e=>((e,t)=>{if(t<=0)return void nt(e);const a=L[e],i=n.find(e=>e.id===a.produitId);if(i&&t>i.stock)return void R(`Quantité maximale disponible: ${i.stock}`);const r=[...L];r[e]={...a,quantite:t,totalCDF:t*a.prixUnitaireCDF,totalUSD:a.prixUnitaireUSD?t*a.prixUnitaireUSD:void 0},q(r),R("")})(t,e),min:1,max:(null==(r=n.find(t=>t.id===e.produitId))?void 0:r.stock)||999,size:"small",showButtons:!0,allowDirectInput:!0}),a.jsx(o,{variant:"subtitle1",sx:{minWidth:"120px",textAlign:"right",fontWeight:"bold",color:"primary.main"},children:s.total}),a.jsx(U,{size:"small",color:"error",onClick:()=>nt(t),title:"Supprimer l'article",children:a.jsx(_e,{fontSize:"small"})})]})})]}),t<L.length-1&&a.jsx(h,{})]},t)}),a.jsx(h,{sx:{my:2}}),a.jsx(x,{sx:{bgcolor:"primary.50",borderRadius:1},children:a.jsx(y,{primary:a.jsxs(o,{variant:"h6",sx:{fontWeight:"bold",color:"primary.main"},children:["Total: ",(()=>{const e=at();return"USD"===se?`$${e.USD.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`:`${e.CDF.toLocaleString("fr-FR")} CDF`})()]}),secondary:a.jsx(o,{variant:"body2",color:"text.secondary",children:"USD"===se?`≈ ${Bi(at().CDF,"CDF")} (taux: ${(null==we?void 0:we.tauxChangeUSDCDF)||2800})`:`≈ ${Bi(at().USD,"USD")} (taux: ${(null==we?void 0:we.tauxChangeUSDCDF)||2800})`})})})]})]}),a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"h6",gutterBottom:!0,children:"Informations Client"}),a.jsxs(ce,{container:!0,spacing:2,children:[a.jsx(ce,{item:!0,xs:12,md:4,children:a.jsx(ne,{fullWidth:!0,label:"credit"===ie?"Nom du client *":"Nom du client",placeholder:"credit"===ie?"Nom requis pour crédit":"Client (par défaut)",value:W,onChange:e=>X(e.target.value),required:"credit"===ie,error:"credit"===ie&&!W.trim()&&k.includes("nom du client"),helperText:"credit"===ie&&!W.trim()&&k.includes("nom du client")?"Le nom du client est obligatoire pour les ventes à crédit":""})}),a.jsx(ce,{item:!0,xs:12,md:4,children:a.jsx(ne,{fullWidth:!0,label:"Téléphone",value:Q,onChange:e=>H(e.target.value)})}),a.jsx(ce,{item:!0,xs:12,md:4,children:a.jsx(ne,{fullWidth:!0,label:"Adresse",value:G,onChange:e=>Y(e.target.value)})})]})]}),a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"h6",gutterBottom:!0,children:"Informations de Paiement"}),a.jsxs(ce,{container:!0,spacing:2,children:[a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(je,{component:"fieldset",children:[a.jsx(Ce,{component:"legend",children:"Type de vente"}),a.jsxs(We,{row:!0,value:ie,onChange:e=>re(e.target.value),children:[a.jsx(Xe,{value:"cash",control:a.jsx(Qe,{}),label:"Cash"}),a.jsx(Xe,{value:"credit",control:a.jsx(Qe,{}),label:"Crédit"})]})]})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Méthode de paiement"}),a.jsxs(Se,{value:K,label:"Méthode de paiement",onChange:e=>Z(e.target.value),children:[a.jsx(J,{value:"cash",children:"Cash"}),a.jsx(J,{value:"banque",children:"Banque"}),a.jsx(J,{value:"mobile_money",children:"Mobile Money"})]})]})}),"credit"===ie&&a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Date d'échéance",type:"date",value:le,onChange:e=>de(e.target.value),InputLabelProps:{shrink:!0}})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:2,value:ue,onChange:e=>ve(e.target.value)})})]})]})]})]}),a.jsxs(Le,{children:[a.jsx(Xe,{control:a.jsx(Je,{checked:be,onChange:e=>fe(e.target.checked)}),label:"Imprimer reçu",sx:{mr:"auto"}}),Oe&&a.jsxs(i,{display:"flex",alignItems:"center",gap:1,mr:2,children:[a.jsx(qe,{size:20}),a.jsx(o,{variant:"body2",children:"Génération du reçu..."})]}),a.jsx(I,{onClick:et,children:"Annuler"}),a.jsx(I,{onClick:async()=>{var a;if(0===L.length)return void R("Veuillez ajouter au moins un produit");if("credit"===ie&&!W.trim())return void R("Le nom du client est obligatoire pour les ventes à crédit");if("credit"===ie&&!le)return void R("La date d'échéance est requise pour les ventes à crédit");const i=at(),s=(new Date).toISOString(),o=be||(null==(a=null==we?void 0:we.impression)?void 0:a.impressionAutomatique)||!1,l=o?await er.generateSalesReceiptNumber():void 0,c={id:`VTE-${Date.now()}`,produits:L,nomClient:W.trim()||"Client",telephoneClient:Q.trim()||void 0,adresseClient:G.trim()||void 0,totalCDF:i.CDF,totalUSD:i.USD,methodePaiement:K,typeVente:ie,datevente:s,vendeur:(null==Ge?void 0:Ge.nom)||"Inconnu",notes:ue.trim()||void 0,numeroRecu:l},d=n.map(e=>{const t=L.find(t=>t.produitId===e.id);return t?{...e,stock:e.stock-t.quantite,dateModification:s}:e}),u=[...e,c];if(t(u),r(d),await mi.setSales(u),await mi.setProducts(d),"credit"===ie){const e={id:`DET-${Date.now()}`,venteId:c.id,nomClient:W.trim()||"Client",telephoneClient:Q.trim()||void 0,adresseClient:G.trim()||void 0,montantTotalCDF:i.CDF,montantTotalUSD:i.USD,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:i.CDF,montantRestantUSD:i.USD,dateCreation:s,dateEcheance:le,statut:"active",statutPaiement:"impaye",paiements:[],notes:ue.trim()||void 0,deviseVente:se},t=await mi.getDebts();await mi.setDebts([...t,e])}if(N("Vente enregistrée avec succès"),o)try{Ve(!0);const e=await er.createSalesReceiptData(c);Re(e),Te(!0)}catch(m){console.error("Erreur lors de la génération du reçu:",m),R("Erreur lors de la génération du reçu")}finally{Ve(!1)}setTimeout(()=>{et()},1500)},variant:"contained",disabled:0===L.length||Oe,children:"Enregistrer la Vente"})]})]}),a.jsxs(Ae,{open:w,onClose:()=>E(!1),maxWidth:"md",fullWidth:!0,children:[a.jsx(Ie,{children:"Détails de la Vente"}),a.jsx(Ne,{children:P&&a.jsxs(ce,{container:!0,spacing:2,children:[a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"subtitle2",sx:{fontSize:"1.1rem",fontWeight:"bold"},children:"Produits Vendus:"}),a.jsx(o,{variant:"h6",color:"primary",sx:{mt:.5,mb:1},children:P.produits.map(e=>e.nomProduit).join(", ")})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Référence Vente:"}),a.jsx(o,{variant:"body2",color:"text.secondary",children:P.id})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Date:"}),a.jsx(o,{variant:"body1",children:Ut(new Date(P.datevente),"dd/MM/yyyy HH:mm",{locale:Di})})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Client:"}),a.jsx(o,{variant:"body1",children:P.nomClient}),P.telephoneClient&&a.jsx(o,{variant:"body2",color:"text.secondary",children:P.telephoneClient})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Vendeur:"}),a.jsx(o,{variant:"body1",children:P.vendeur})]}),a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Produits:"}),a.jsxs(p,{dense:!0,children:[P.produits.map((e,t)=>a.jsxs(x,{children:[a.jsx(y,{primary:e.nomProduit,secondary:a.jsxs(i,{children:[a.jsxs(o,{variant:"body2",component:"span",children:[e.quantite," × ",Bi(e.prixUnitaireCDF,"CDF")]}),a.jsxs(o,{variant:"caption",display:"block",color:"text.secondary",children:["≈ ",e.quantite," × ",Bi(e.prixUnitaireCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),"USD")]})]})}),a.jsxs(i,{textAlign:"right",children:[a.jsx(o,{variant:"body2",fontWeight:"medium",children:Bi(e.totalCDF,"CDF")}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ ",Bi(e.totalCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),"USD")]})]})]},t)),a.jsx(h,{}),a.jsx(x,{children:a.jsx(y,{primary:a.jsxs(i,{children:[a.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:["Total: ",_i(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).primaryAmount," ",_i(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).primaryCurrency]}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",_i(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).secondaryAmount]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["Taux de change: 1 USD = ",(null==we?void 0:we.tauxChangeUSDCDF)||2800," CDF"]})]})})})]})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Méthode de paiement:"}),a.jsx(A,{icon:ot(P.methodePaiement),label:lt(P.methodePaiement)})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Type de vente:"}),a.jsx(A,{label:"cash"===P.typeVente?"Cash":"Crédit",color:"cash"===P.typeVente?"success":"warning"})]}),P.notes&&a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"subtitle2",children:"Notes:"}),a.jsx(o,{variant:"body1",children:P.notes})]})]})}),a.jsx(Le,{children:a.jsx(I,{onClick:()=>E(!1),children:"Fermer"})})]}),a.jsx(ar,{open:Ue,onClose:()=>Te(!1),receiptData:ke,onPrintSuccess:()=>{N("Reçu imprimé avec succès"),setTimeout(()=>N(""),3e3)}})]})},rr=()=>{const[e,t]=Ct.useState([]),[n,r]=Ct.useState([]),[s,l]=Ct.useState(""),[d,u]=Ct.useState("all"),[m,g]=Ct.useState("all"),[j,D]=Ct.useState(0),[S,C]=Ct.useState(10),[b,w]=Ct.useState(!1),[E,P]=Ct.useState(!1),[T,k]=Ct.useState(null),[R,M]=Ct.useState(0),[N,L]=Ct.useState("cash"),[V,q]=Ct.useState(""),[B,$]=Ct.useState(""),[W,X]=Ct.useState(""),[Q,H]=Ct.useState({tauxChangeUSDCDF:2800}),[Y,K]=Ct.useState([]),[Z,ie]=Ct.useState(!1),[re,oe]=Ct.useState("CDF"),de=hi.getUserPermissions(),ue=e=>Y.find(t=>t.id===e.venteId),ve=e=>{const t=ue(e);return t&&t.produits&&0!==t.produits.length?t.produits.map(e=>`${e.nomProduit} (x${e.quantite})`).join(", "):"Produits non disponibles"},Ce=e=>{if(e.nomClient&&""!==e.nomClient.trim()&&"Client"!==e.nomClient)return e.nomClient;const t=ue(e);return t&&t.nomClient&&""!==t.nomClient.trim()&&"Client"!==t.nomClient?t.nomClient:"Client"},be=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(t){return console.warn("Error parsing date:",e,t),null}};Ct.useEffect(()=>{fe();(async()=>{0===(await mi.getDebts()).length&&(console.log("No debts found, forcing initialization..."),await mi.forceInitializeDebts(),setTimeout(()=>fe(),500))})()},[]),Ct.useEffect(()=>{we()},[e,s,d,m]),Ct.useEffect(()=>{-1===S&&C(n.length||1)},[n.length,S]);const fe=async()=>{try{const e=await mi.getDebts(),n=await mi.getSettings(),a=await mi.getSales(),i=e.filter(e=>e.id&&e.venteId?(e.nomClient&&""!==e.nomClient.trim()||(e.nomClient="Client"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,!0):(console.warn("Invalid debt record missing required fields:",e),!1)).map(e=>{try{const t=We(e);if("paid"===t.statut)return t;const n=new Date,a=be(t.dateEcheance);return t.montantRestantCDF<=0?{...t,statut:"paid",statutPaiement:"paye"}:a&&Mt(n,a)?{...t,statut:"overdue"}:{...t,statut:"active"}}catch(t){return console.warn("Error processing debt status:",e,t),e}});t(i),H(n),K(a),await mi.setDebts(i)}catch(e){console.error("Error loading debt data:",e),$("Erreur lors du chargement des données de dette.")}},we=()=>{try{let t=e;if(s&&s.trim()){const e=s.toLowerCase().trim();t=t.filter(t=>{var n,a,i,r;try{const s=(null==(n=t.nomClient)?void 0:n.toLowerCase())||"",o=(null==(a=t.id)?void 0:a.toLowerCase())||"",l=(null==(i=t.venteId)?void 0:i.toLowerCase())||"",c=(null==(r=t.telephoneClient)?void 0:r.toLowerCase())||"",d=ve(t).toLowerCase();return s.includes(e)||o.includes(e)||l.includes(e)||c.includes(e)||d.includes(e)}catch(s){return console.warn("Error filtering debt:",t,s),!1}})}"all"!==d&&(t=t.filter(e=>e.statut===d)),"all"!==m&&(t=t.filter(e=>e.statutPaiement===m)),r(t)}catch(t){console.error("Error in filterDebts:",t),r(e),$("Erreur lors de la recherche. Affichage de toutes les dettes.")}},Ee=e=>{switch(e){case"active":return"primary";case"overdue":return"error";case"paid":return"success";default:return"default"}},Pe=e=>{switch(e){case"active":return"Active";case"overdue":return"En retard";case"paid":return"Payée";default:return e}},Te=e=>{switch(e){case"active":return a.jsx(Ge,{});case"overdue":return a.jsx(z,{});case"paid":return a.jsx(_,{});default:return a.jsx(v,{})}},ke=e=>{switch(e){case"paye":return"Payé";case"impaye":return"Impayé";default:return e}},Re=e=>{switch(e){case"paye":return a.jsx(Ye,{});case"impaye":return a.jsx(et,{});default:return a.jsx(v,{})}},Oe=()=>{w(!1),k(null),M(0),L("cash"),q(""),oe("CDF"),$(""),X("")},Ve=e=>{k(e),P(!0)},qe=()=>{P(!1),k(null),ie(!1),M(0),q(""),oe("CDF"),$(""),X("")},Be=async()=>{if(!T)return;if(R<=0)return void $("Le montant doit être supérieur à 0");if(R>T.montantRestantCDF)return void $("Le montant ne peut pas dépasser le montant restant");const n=(new Date).toISOString();let a,i;"USD"===re?(i=R/Q.tauxChangeUSDCDF,a=R):(a=R,i=R/Q.tauxChangeUSDCDF);const r={id:`PAY-${Date.now()}`,montantCDF:a,montantUSD:i,methodePaiement:N,datePaiement:n,notes:V.trim()||void 0,deviseOriginale:re},s={...T,montantPayeCDF:T.montantPayeCDF+R,montantPayeUSD:(T.montantPayeUSD||0)+R/Q.tauxChangeUSDCDF,paiements:[...T.paiements,r]},o=We(s),l={...o,statut:o.montantRestantCDF<=0?"paid":T.statut,statutPaiement:Je(o)},c=e.map(e=>e.id===T.id?l:e);t(c),await mi.setDebts(c),k(l),X("Paiement enregistré avec succès"),Z?setTimeout(()=>{ie(!1),M(0),q(""),oe("CDF"),$(""),X("")},2e3):setTimeout(()=>{Oe()},1500)},_e=e=>{switch(e){case"cash":default:return a.jsx(f,{});case"banque":return a.jsx($e,{});case"mobile_money":return a.jsx(He,{})}},ze=e=>{switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}},We=e=>{const t=e.paiements.reduce((e,t)=>e+t.montantCDF,0),n=e.paiements.reduce((e,t)=>e+(t.montantUSD||0),0),a=e.paiements.length>0?t:e.montantPayeCDF,i=e.paiements.length>0?n:e.montantPayeUSD||0,r=e.montantTotalCDF-a,s=e.montantTotalUSD?e.montantTotalUSD-i:void 0,o=e.montantTotalUSD||e.montantTotalCDF/Q.tauxChangeUSDCDF,l=i||a/Q.tauxChangeUSDCDF,c=void 0!==s?Math.max(0,s):Math.max(0,r/Q.tauxChangeUSDCDF);return{...e,montantTotalCDF:e.montantTotalCDF,montantTotalUSD:o,montantPayeCDF:a,montantPayeUSD:l,montantRestantCDF:Math.max(0,r),montantRestantUSD:c}},Qe=e=>0===e.montantTotalCDF?100:Math.min(100,e.montantPayeCDF/e.montantTotalCDF*100),Je=e=>e.montantPayeCDF>=e.montantTotalCDF?"paye":"impaye",tt=e=>"paye"===Je(e)?"Payé":"Impayé",nt=e=>"paye"===Je(e)?"success":"error",at=e=>"paye"===Je(e)?a.jsx(_,{fontSize:"small"}):a.jsx(Ze,{fontSize:"small"}),it=e.filter(e=>"active"===e.statut),rt=e.filter(e=>"overdue"===e.statut),st=e.filter(e=>"paye"===e.statutPaiement),ot=e.filter(e=>"impaye"===e.statutPaiement),lt=e.reduce((e,t)=>e+t.montantTotalCDF,0),ct=e.reduce((e,t)=>e+t.montantRestantCDF,0);return a.jsxs(i,{children:[a.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[a.jsx(o,{variant:"h4",children:"Gestion des Dettes"}),a.jsx(I,{variant:"contained",startIcon:a.jsx(Ue,{}),onClick:()=>{const e=`SmartBoutique_Dettes_${(new Date).toISOString().split("T")[0]}.csv`;Oa.downloadCSV(n,_a,e),X("Dettes exportées en CSV avec succès (compatible Excel)"),setTimeout(()=>X(""),3e3)},children:"Exporter les Dettes"})]}),W&&a.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>X(""),children:W}),a.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dettes Actives"}),a.jsx(o,{variant:"h6",children:it.length}),a.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[_i(it.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryAmount," ",_i(it.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_i(it.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).secondaryAmount]})]}),a.jsx(Ge,{color:"primary",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"En Retard"}),a.jsx(o,{variant:"h6",color:"error.main",children:rt.length}),a.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[_i(rt.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryAmount," ",_i(rt.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_i(rt.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).secondaryAmount]})]}),a.jsx(z,{color:"error",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Statut Payé"}),a.jsx(o,{variant:"h6",color:"success.main",children:st.length}),a.jsxs(o,{variant:"body2",color:"error",children:["Impayé: ",ot.length]})]}),a.jsx(Ye,{color:"success",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Restant"}),a.jsxs(o,{variant:"h6",fontWeight:"medium",children:[_i(ct,Q.tauxChangeUSDCDF).primaryAmount," ",_i(ct,Q.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",_i(ct,Q.tauxChangeUSDCDF).secondaryAmount," sur ",_i(lt,Q.tauxChangeUSDCDF).primaryAmount," ",_i(lt,Q.tauxChangeUSDCDF).primaryCurrency]})]}),a.jsx(v,{color:"warning",sx:{fontSize:40}})]})})})})]}),a.jsx(c,{sx:{p:2,mb:3},children:a.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[a.jsx(ce,{item:!0,xs:12,md:4,children:a.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par client, produit, ID dette ou ID vente...",value:s,onChange:e=>l(e.target.value),InputProps:{startAdornment:a.jsx(ae,{position:"start",children:a.jsx(Fe,{})})}})}),a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Statut"}),a.jsxs(Se,{value:d,label:"Statut",onChange:e=>u(e.target.value),children:[a.jsx(J,{value:"all",children:"Tous"}),a.jsx(J,{value:"active",children:"Actives"}),a.jsx(J,{value:"overdue",children:"En retard"}),a.jsx(J,{value:"paid",children:"Payées"})]})]})}),a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Statut Paiement"}),a.jsxs(Se,{value:m,label:"Statut Paiement",onChange:e=>g(e.target.value),children:[a.jsx(J,{value:"all",children:"Tous"}),a.jsx(J,{value:"paye",children:"Payé"}),a.jsx(J,{value:"impaye",children:"Impayé"})]})]})})]})}),a.jsxs(me,{component:c,children:[a.jsxs(he,{children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:"Client"}),a.jsx(ge,{children:"Montant Dû"}),a.jsx(ge,{align:"right",children:"Payé"}),a.jsx(ge,{align:"right",children:"Restant"}),a.jsx(ge,{align:"center",children:"Progression"}),a.jsx(ge,{align:"center",children:"Statut"}),a.jsx(ge,{align:"center",children:"Statut Paiement"}),a.jsx(ge,{children:"Échéance"}),de.canManageDebts&&a.jsx(ge,{align:"center",children:"Actions"})]})}),a.jsx(ye,{children:(-1===S?n:n.slice(j*S,j*S+S)).map(e=>{try{const t=Qe(e),n="overdue"===e.statut;return a.jsxs(xe,{hover:!0,onClick:()=>Ve(e),sx:{cursor:"pointer"},children:[a.jsx(ge,{children:a.jsxs(i,{children:[a.jsx(o,{variant:"h6",fontWeight:"bold",color:"primary",gutterBottom:!0,children:Ce(e)}),e.telephoneClient&&a.jsxs(o,{variant:"body2",color:"text.secondary",display:"block",sx:{mb:.5},children:["📞 ",e.telephoneClient]}),a.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",display:"block",sx:{mb:.5},children:["🛍️ Produits: ",ve(e)]}),a.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",children:["Créé le: ",(()=>{try{const t=be(e.dateCreation);return t?Ut(t,"dd/MM/yyyy",{locale:Di}):"Date invalide"}catch(t){return console.warn("Error formatting creation date:",e.dateCreation,t),"Date invalide"}})()," • ID: ",e.venteId||"N/A"]})]})}),a.jsx(ge,{align:"right",children:Bi(e.montantTotalCDF,"CDF")}),a.jsx(ge,{align:"right",children:Bi(e.montantPayeCDF,"CDF")}),a.jsx(ge,{align:"right",children:a.jsx(o,{variant:"body2",color:e.montantRestantCDF>0?"error":"success.main",children:Bi(e.montantRestantCDF,"CDF")})}),a.jsx(ge,{align:"center",children:a.jsxs(i,{sx:{width:100},children:[a.jsx(le,{variant:"determinate",value:t,color:100===t?"success":n?"error":"primary"}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:[Math.round(t),"%"]})]})}),a.jsx(ge,{align:"center",children:a.jsx(A,{icon:Te(e.statut),label:Pe(e.statut),color:Ee(e.statut),size:"small"})}),a.jsx(ge,{align:"center",children:a.jsx(A,{icon:at(e),label:tt(e),color:nt(e),size:"small"})}),a.jsx(ge,{children:a.jsx(o,{variant:"body2",color:n?"error":"text.primary",children:(()=>{const t=be(e.dateEcheance);return t?Ut(t,"dd/MM/yyyy",{locale:Di}):"Date invalide"})()})}),de.canManageDebts&&a.jsx(ge,{align:"center",children:a.jsxs(i,{display:"flex",gap:1,children:[a.jsx(F,{title:"Voir détails",children:a.jsx(U,{size:"small",onClick:()=>Ve(e),children:a.jsx(se,{fontSize:"small"})})}),"paid"!==e.statut&&a.jsx(F,{title:"Ajouter paiement",children:a.jsx(U,{size:"small",color:"primary",onClick:()=>(e=>{k(e),M(e.montantRestantCDF),L("cash"),q(""),oe("CDF"),w(!0),$(""),X("")})(e),children:a.jsx(G,{fontSize:"small"})})})]})})]},e.id)}catch(t){return console.error("Error rendering debt row:",e,t),a.jsx(xe,{children:a.jsx(ge,{colSpan:de.canManageDebts?9:8,children:a.jsxs(o,{color:"error",variant:"body2",children:["Erreur d'affichage pour cette dette. ID: ",e.id||"Inconnu"]})})},e.id||`error-${Math.random()}`)}})})]}),a.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:n.length,rowsPerPage:-1===S?n.length:S,page:-1===S?0:j,onPageChange:(e,t)=>{-1!==S&&D(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);C(t),D(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===S?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),a.jsxs(Ae,{open:b,onClose:Oe,maxWidth:"sm",fullWidth:!0,children:[a.jsx(Ie,{children:"Ajouter un Paiement"}),a.jsxs(Ne,{children:[B&&a.jsx(O,{severity:"error",sx:{mb:2},children:B}),W&&a.jsx(O,{severity:"success",sx:{mb:2},children:W}),T&&a.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[a.jsxs(ce,{item:!0,xs:12,children:[a.jsxs(o,{variant:"subtitle2",gutterBottom:!0,children:["Client: ",Ce(T)]}),a.jsxs(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Montant restant: ",Bi(T.montantRestantCDF,"CDF")]})]}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(Yi,{label:"Montant du paiement",value:R,onChange:e=>M(e),min:0,max:T.montantRestantCDF,step:50,exchangeRate:Q.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,onCurrencyModeChange:e=>oe(e)})}),a.jsx(ce,{item:!0,xs:12,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Méthode de paiement"}),a.jsxs(Se,{value:N,label:"Méthode de paiement",onChange:e=>L(e.target.value),children:[a.jsx(J,{value:"cash",children:"Cash"}),a.jsx(J,{value:"banque",children:"Banque"}),a.jsx(J,{value:"mobile_money",children:"Mobile Money"})]})]})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:2,value:V,onChange:e=>q(e.target.value)})})]})]}),a.jsxs(Le,{children:[a.jsx(I,{onClick:Oe,children:"Annuler"}),a.jsx(I,{onClick:Be,variant:"contained",disabled:!T||R<=0,children:"Enregistrer le Paiement"})]})]}),a.jsxs(Ae,{open:E,onClose:qe,maxWidth:"md",fullWidth:!0,children:[a.jsx(Ie,{children:"Détails de la Dette"}),a.jsx(Ne,{children:T&&a.jsxs(ce,{container:!0,spacing:2,children:[a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"subtitle2",sx:{fontSize:"1.1rem",fontWeight:"bold"},children:"Produits Achetés à Crédit:"}),a.jsx(o,{variant:"h6",color:"primary",sx:{mt:.5,mb:1},children:ve(T)})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"ID Dette:"}),a.jsx(o,{variant:"body1",children:T.id})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Référence Vente:"}),a.jsx(o,{variant:"body2",color:"text.secondary",children:T.venteId})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Client:"}),a.jsx(o,{variant:"body1",sx:{fontWeight:"medium"},children:Ce(T)}),T.telephoneClient&&a.jsxs(i,{sx:{mt:.5},children:[a.jsx(o,{variant:"caption",color:"text.secondary",children:"Téléphone:"}),a.jsx(o,{variant:"body2",color:"primary",children:T.telephoneClient})]}),T.adresseClient&&a.jsxs(i,{sx:{mt:.5},children:[a.jsx(o,{variant:"caption",color:"text.secondary",children:"Adresse:"}),a.jsx(o,{variant:"body2",color:"text.primary",children:T.adresseClient})]})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Statut:"}),a.jsx(A,{icon:Te(T.statut),label:Pe(T.statut),color:Ee(T.statut)})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Statut Paiement:"}),a.jsx(i,{display:"flex",alignItems:"center",gap:1,sx:{mt:.5},children:de.canManageDebts?a.jsx(Xe,{control:a.jsx(Ke,{checked:"paye"===T.statutPaiement,onChange:n=>(async(n,a)=>{if(!de.canManageDebts)return;if(!window.confirm(`Êtes-vous sûr de vouloir changer le statut de paiement à "${ke(a)}" ?\n\nCette action modifiera le statut de paiement de la dette.`))return;const i=e.map(e=>e.id===n?{...e,statutPaiement:a}:e);t(i),await mi.setDebts(i),T&&T.id===n&&k({...T,statutPaiement:a}),X(`Statut de paiement mis à jour: ${ke(a)}`),setTimeout(()=>{X("")},3e3)})(T.id,n.target.checked?"paye":"impaye"),color:"success",size:"small"}),label:a.jsxs(i,{display:"flex",alignItems:"center",gap:.5,children:[Re(T.statutPaiement),a.jsx(o,{variant:"body2",children:ke(T.statutPaiement)})]}),labelPlacement:"end"}):a.jsx(A,{icon:Re(T.statutPaiement),label:ke(T.statutPaiement),color:(e=>{switch(e){case"paye":return"success";case"impaye":return"error";default:return"default"}})(T.statutPaiement)})})]}),a.jsxs(ce,{item:!0,xs:12,md:4,children:[a.jsx(o,{variant:"subtitle2",children:"Montant Dû:"}),a.jsx(o,{variant:"body1",color:"primary",sx:{fontWeight:"bold"},children:Bi(T.montantTotalCDF,"CDF")})]}),a.jsxs(ce,{item:!0,xs:12,md:4,children:[a.jsx(o,{variant:"subtitle2",children:"Montant Payé:"}),a.jsx(o,{variant:"body1",color:"success.main",children:Bi(T.montantPayeCDF,"CDF")})]}),a.jsxs(ce,{item:!0,xs:12,md:4,children:[a.jsx(o,{variant:"subtitle2",children:"Montant Restant:"}),a.jsx(o,{variant:"body1",color:"error",children:Bi(T.montantRestantCDF,"CDF")})]}),a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Progression du Paiement:"}),a.jsxs(i,{sx:{width:"100%",mb:1},children:[a.jsx(le,{variant:"determinate",value:Qe(T),color:100===Qe(T)?"success":"overdue"===T.statut?"error":"primary",sx:{height:10,borderRadius:5}}),a.jsxs(i,{sx:{display:"flex",justifyContent:"space-between",mt:1},children:[a.jsxs(o,{variant:"caption",color:"text.secondary",children:[Math.round(Qe(T)),"% payé"]}),a.jsx(o,{variant:"caption",color:"text.secondary",children:T.montantRestantCDF>0?`${Bi(T.montantRestantCDF,"CDF")} restant`:"Entièrement payé"})]})]})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Date de Création:"}),a.jsx(o,{variant:"body1",children:(()=>{const e=be(T.dateCreation);return e?Ut(e,"dd/MM/yyyy",{locale:Di}):"Date invalide"})()})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsx(o,{variant:"subtitle2",children:"Date d'Échéance:"}),a.jsx(o,{variant:"body1",color:"overdue"===T.statut?"error":"text.primary",children:(()=>{const e=be(T.dateEcheance);return e?Ut(e,"dd/MM/yyyy",{locale:Di}):"Date invalide"})()})]}),a.jsxs(ce,{item:!0,xs:12,children:[a.jsxs(o,{variant:"subtitle2",gutterBottom:!0,children:["Historique des Paiements (",T.paiements.length,")"]}),0===T.paiements.length?a.jsx(O,{severity:"info",children:"Aucun paiement enregistré"}):a.jsx(p,{dense:!0,children:T.paiements.map((e,t)=>a.jsxs(ft.Fragment,{children:[a.jsx(x,{children:a.jsx(y,{primary:a.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[_e(e.methodePaiement),a.jsx(o,{variant:"body2",children:"USD"===e.deviseOriginale?`${Bi(e.montantUSD||0,"USD")} (≈ ${Bi(e.montantCDF,"CDF")})`:`${Bi(e.montantCDF,"CDF")} (≈ ${Bi(e.montantUSD||0,"USD")})`}),a.jsx(A,{label:ze(e.methodePaiement),size:"small"})]}),secondary:a.jsxs(i,{children:[a.jsx(o,{variant:"caption",children:(()=>{const t=be(e.datePaiement);return t?Ut(t,"dd/MM/yyyy HH:mm",{locale:Di}):"Date invalide"})()}),e.notes&&a.jsx(o,{variant:"caption",display:"block",children:e.notes})]})})}),t<T.paiements.length-1&&a.jsx(h,{})]},e.id))})]}),de.canManageDebts&&"paid"!==T.statut&&Z&&a.jsx(ce,{item:!0,xs:12,children:a.jsxs(c,{elevation:2,sx:{p:2,mt:2,bgcolor:"background.default"},children:[a.jsxs(o,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[a.jsx(G,{}),"Ajouter un Paiement"]}),a.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[a.jsx(ce,{item:!0,xs:12,children:a.jsxs(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Montant restant: ",Bi(T.montantRestantCDF,"CDF")]})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(Yi,{label:"Montant à payer",value:R,onChange:e=>M(e),min:0,max:T.montantRestantCDF,step:50,exchangeRate:Q.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,onCurrencyModeChange:e=>oe(e)})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Méthode de paiement"}),a.jsxs(Se,{value:N,onChange:e=>L(e.target.value),label:"Méthode de paiement",children:[a.jsx(J,{value:"cash",children:a.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[a.jsx(f,{fontSize:"small"}),"Cash"]})}),a.jsx(J,{value:"mobile_money",children:a.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[a.jsx(He,{fontSize:"small"}),"Mobile Money"]})}),a.jsx(J,{value:"banque",children:a.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[a.jsx($e,{fontSize:"small"}),"Banque"]})})]})]})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Notes (optionnel)",value:V,onChange:e=>q(e.target.value),multiline:!0,rows:2,placeholder:"Ajouter des notes sur ce paiement..."})}),B&&a.jsx(ce,{item:!0,xs:12,children:a.jsx(O,{severity:"error",children:B})}),W&&a.jsx(ce,{item:!0,xs:12,children:a.jsx(O,{severity:"success",children:W})}),a.jsx(ce,{item:!0,xs:12,children:a.jsxs(i,{display:"flex",gap:1,justifyContent:"flex-end",children:[a.jsx(I,{variant:"outlined",onClick:()=>{ie(!1),M(0),q(""),oe("CDF"),$(""),X("")},children:"Annuler"}),a.jsx(I,{variant:"contained",color:"success",onClick:()=>{M(T.montantRestantCDF),q("Paiement complet")},startIcon:a.jsx(G,{}),children:"Paiement Complet"}),a.jsx(I,{variant:"contained",color:"primary",onClick:Be,startIcon:a.jsx(G,{}),disabled:R<=0,children:"Payer"})]})})]})]})}),T.notes&&a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"subtitle2",children:"Notes:"}),a.jsx(o,{variant:"body1",children:T.notes})]})]})}),a.jsxs(Le,{children:[de.canManageDebts&&T&&"paid"!==T.statut&&!Z&&a.jsx(I,{variant:"contained",color:"primary",onClick:()=>{ie(!0),M(0),L("cash"),q(""),$(""),X("")},startIcon:a.jsx(G,{}),sx:{mr:1},children:"Ajouter Paiement"}),a.jsx(I,{onClick:qe,children:"Fermer"})]})]})]})},sr=(e,t="dd/MM/yyyy")=>{try{const n="string"==typeof e?new Date(e):e;return Lt(n)?Ut(n,t,{locale:Di}):"Date invalide"}catch(fr){return console.warn("Invalid date value:",e,fr),"Date invalide"}},or=()=>{var e;try{const[t,n]=Ct.useState([]),[r,s]=Ct.useState([]),[l,d]=Ct.useState(""),[u,m]=Ct.useState(""),[h,p]=Ct.useState(""),[x,g]=Ct.useState(0),[y,j]=Ct.useState(10),[D,S]=Ct.useState(!1),[v,C]=Ct.useState(null),[b,w]=Ct.useState({description:"",montantCDF:0,categorie:"",dateDepense:sr(new Date,"yyyy-MM-dd"),notes:""}),[E,P]=Ct.useState(""),[T,k]=Ct.useState(""),[R,M]=Ct.useState({tauxChangeUSDCDF:2800}),[N,L]=Ct.useState(!1),[V,B]=Ct.useState(!1),[_,$]=Ct.useState(null),[z,W]=Ct.useState(!1),X=hi.getUserPermissions(),Q=hi.getCurrentUser(),H=["Loyer","Électricité","Eau","Internet","Téléphone","Transport","Carburant","Fournitures de bureau","Marketing","Maintenance","Assurance","Taxes","Salaires","Formation","Équipement","Autres"];Ct.useEffect(()=>{G()},[]),Ct.useEffect(()=>{Y()},[t,l,u,h]),Ct.useEffect(()=>{-1===y&&j(r.length||1)},[r.length,y]);const G=async()=>{const e=await mi.getExpenses(),t=await mi.getSettings();n(e),M(t)},Y=()=>{let e=t;if(l&&(e=e.filter(e=>e.description.toLowerCase().includes(l.toLowerCase())||e.categorie.toLowerCase().includes(l.toLowerCase())||e.notes&&e.notes.toLowerCase().includes(l.toLowerCase()))),u&&(e=e.filter(e=>e.categorie===u)),h){const t=new Date;let n,a;switch(h){case"today":n=new Date(t.getFullYear(),t.getMonth(),t.getDate()),a=new Date(t.getFullYear(),t.getMonth(),t.getDate(),23,59,59);break;case"this_month":n=It(t),a=Nt(t);break;case"last_month":const e=new Date(t.getFullYear(),t.getMonth()-1,1);n=It(e),a=Nt(e);break;default:n=new Date(0),a=new Date}e=e.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&Ot(t,{start:n,end:a})})}s(e)},K=e=>{e?(C(e),w({description:e.description,montantCDF:e.montantCDF,categorie:e.categorie,dateDepense:sr(e.dateDepense,"yyyy-MM-dd"),notes:e.notes||""})):(C(null),w({description:"",montantCDF:0,categorie:"",dateDepense:sr(new Date,"yyyy-MM-dd"),notes:""})),S(!0),P(""),k("")},Z=()=>{S(!1),C(null),P(""),k(""),L(!1)},ie=async()=>{var e;if(b.description.trim())if(b.montantCDF<=0)P("Le montant doit être supérieur à 0");else if(b.categorie)if(b.dateDepense){if(v){const e={...v,description:b.description.trim(),montantCDF:b.montantCDF,montantUSD:b.montantCDF/R.tauxChangeUSDCDF,categorie:b.categorie,dateDepense:b.dateDepense,notes:b.notes.trim()||void 0},a=t.map(t=>t.id===v.id?e:t);n(a),await mi.setExpenses(a),k("Dépense mise à jour avec succès")}else{const i=N||(null==(e=R.impression)?void 0:e.impressionAutomatique)||!1,r=i?await er.generateExpenseReceiptNumber():void 0,s={id:Date.now().toString(),description:b.description.trim(),montantCDF:b.montantCDF,montantUSD:b.montantCDF/R.tauxChangeUSDCDF,categorie:b.categorie,dateDepense:b.dateDepense,notes:b.notes.trim()||void 0,creePar:(null==Q?void 0:Q.nom)||"Inconnu",numeroRecu:r},o=[...t,s];if(n(o),await mi.setExpenses(o),k("Dépense créée avec succès"),i)try{W(!0);const e=await er.createExpenseReceiptData(s);$(e),B(!0)}catch(a){console.error("Erreur lors de la génération du reçu:",a),P("Erreur lors de la génération du reçu")}finally{W(!1)}}setTimeout(()=>{Z()},1500)}else P("La date est requise");else P("La catégorie est requise");else P("La description est requise")},re=async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer cette dépense "${e.description}" ?`)){const a=t.filter(t=>t.id!==e.id);n(a),await mi.setExpenses(a),k("Dépense supprimée avec succès"),setTimeout(()=>k(""),3e3)}},se=(e,t)=>{-1!==y&&g(t)},oe=e=>{const t=parseInt(e.target.value,10);j(t),g(0)},le=new Date,de={start:It(le),end:Nt(le)},ue=t.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&t.toDateString()===le.toDateString()}),ve=t.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&Ot(t,de)}),Ce=t.length,be=ue.reduce((e,t)=>e+t.montantCDF,0),fe=ve.reduce((e,t)=>e+t.montantCDF,0),we=t.reduce((e,t)=>e+t.montantCDF,0),Ee=t.reduce((e,t)=>(e[t.categorie]=(e[t.categorie]||0)+t.montantUSD,e),{}),Te=Object.entries(Ee).sort(([,e],[,t])=>t-e).slice(0,5),ke=()=>{const e=`SmartBoutique_Depenses_${sr(new Date,"yyyy-MM-dd")}.csv`;Oa.downloadCSV(r,$a,e),k("Dépenses exportées en CSV avec succès (compatible Excel)"),setTimeout(()=>k(""),3e3)};return a.jsxs(i,{children:[a.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[a.jsx(o,{variant:"h4",children:"Gestion des Dépenses"}),a.jsxs(i,{display:"flex",gap:2,children:[a.jsx(I,{variant:"outlined",startIcon:a.jsx(Ue,{}),onClick:ke,children:"Exporter les Dépenses"}),X.canManageExpenses&&a.jsx(I,{variant:"contained",startIcon:a.jsx(Pe,{}),onClick:()=>K(),children:"Nouvelle Dépense"})]})]}),T&&a.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>k(""),children:T}),a.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dépenses du jour"}),a.jsx(o,{variant:"h6",children:ue.length}),a.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[_i(be,R.tauxChangeUSDCDF).primaryAmount," ",_i(be,R.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_i(be,R.tauxChangeUSDCDF).secondaryAmount]})]}),a.jsx(f,{color:"primary",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dépenses du mois"}),a.jsx(o,{variant:"h6",children:ve.length}),a.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[_i(fe,R.tauxChangeUSDCDF).primaryAmount," ",_i(fe,R.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_i(fe,R.tauxChangeUSDCDF).secondaryAmount]})]}),a.jsx(tt,{color:"error",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Dépenses"}),a.jsx(o,{variant:"h6",children:Ce}),a.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[_i(we,R.tauxChangeUSDCDF).primaryAmount," ",_i(we,R.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",_i(we,R.tauxChangeUSDCDF).secondaryAmount]})]}),a.jsx(nt,{color:"info",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Catégories"}),a.jsx(o,{variant:"h6",children:Object.keys(Ee).length}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["Top: ",(null==(e=Te[0])?void 0:e[0])||"N/A"]})]}),a.jsx(at,{color:"warning",sx:{fontSize:40}})]})})})})]}),a.jsx(c,{sx:{p:2,mb:3},children:a.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[a.jsx(ce,{item:!0,xs:12,md:4,children:a.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par description, catégorie ou notes...",value:l,onChange:e=>d(e.target.value),InputProps:{startAdornment:a.jsx(ae,{position:"start",children:a.jsx(Fe,{})})}})}),a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Catégorie"}),a.jsxs(Se,{value:u,label:"Catégorie",onChange:e=>m(e.target.value),children:[a.jsx(J,{value:"",children:"Toutes les catégories"}),H.map(e=>a.jsx(J,{value:e,children:e},e))]})]})}),a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Période"}),a.jsxs(Se,{value:h,label:"Période",onChange:e=>p(e.target.value),children:[a.jsx(J,{value:"",children:"Toutes les périodes"}),a.jsx(J,{value:"today",children:"Aujourd'hui"}),a.jsx(J,{value:"this_month",children:"Ce mois"}),a.jsx(J,{value:"last_month",children:"Mois dernier"})]})]})})]})}),Te.length>0&&a.jsxs(c,{sx:{p:2,mb:3},children:[a.jsx(o,{variant:"h6",gutterBottom:!0,children:"Top 5 Catégories"}),a.jsx(ce,{container:!0,spacing:1,children:Te.map(([e,t])=>a.jsx(ce,{item:!0,children:a.jsx(A,{label:`${e}: ${Bi(t,"CDF")}`,color:"primary",variant:"outlined"})},e))})]}),a.jsxs(me,{component:c,children:[a.jsxs(he,{children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:"Description"}),a.jsx(ge,{children:"Catégorie"}),a.jsx(ge,{align:"right",children:"Montant CDF"}),a.jsx(ge,{align:"right",children:"Montant USD"}),a.jsx(ge,{children:"Date"}),a.jsx(ge,{children:"Créé par"}),X.canManageExpenses&&a.jsx(ge,{align:"center",children:"Actions"})]})}),a.jsx(ye,{children:(-1===y?r:r.slice(x*y,x*y+y)).map(e=>a.jsxs(xe,{hover:!0,onClick:()=>K(e),sx:{cursor:"pointer"},children:[a.jsx(ge,{children:a.jsxs(i,{children:[a.jsx(o,{variant:"subtitle2",children:e.description}),e.notes&&a.jsx(o,{variant:"caption",color:"text.secondary",children:e.notes})]})}),a.jsx(ge,{children:a.jsx(A,{label:e.categorie,size:"small"})}),a.jsx(ge,{align:"right",children:Bi(e.montantCDF,"CDF")}),a.jsx(ge,{align:"right",children:e.montantUSD?Bi(e.montantUSD,"USD"):"-"}),a.jsx(ge,{children:sr(e.dateDepense)}),a.jsx(ge,{children:e.creePar}),X.canManageExpenses&&a.jsx(ge,{align:"center",children:a.jsxs(i,{display:"flex",gap:1,children:[a.jsx(F,{title:"Modifier",children:a.jsx(U,{size:"small",onClick:()=>K(e),children:a.jsx(Re,{fontSize:"small"})})}),hi.hasRole(["super_admin","admin"])&&a.jsx(F,{title:"Supprimer",children:a.jsx(U,{size:"small",color:"error",onClick:()=>re(e),children:a.jsx(q,{fontSize:"small"})})})]})})]},e.id))})]}),a.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:r.length,rowsPerPage:-1===y?r.length:y,page:-1===y?0:x,onPageChange:se,onRowsPerPageChange:oe,labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===y?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),a.jsxs(Ae,{open:D,onClose:Z,maxWidth:"md",fullWidth:!0,children:[a.jsx(Ie,{children:v?"Modifier la Dépense":"Nouvelle Dépense"}),a.jsxs(Ne,{children:[E&&a.jsx(O,{severity:"error",sx:{mb:2},children:E}),T&&a.jsx(O,{severity:"success",sx:{mb:2},children:T}),a.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Description *",value:b.description,onChange:e=>w({...b,description:e.target.value})})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(Yi,{label:"Montant de la dépense",value:b.montantCDF,onChange:e=>w({...b,montantCDF:e}),min:0,max:5e6,step:50,exchangeRate:R.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Catégorie *"}),a.jsx(Se,{value:b.categorie,label:"Catégorie *",onChange:e=>w({...b,categorie:e.target.value}),children:H.map(e=>a.jsx(J,{value:e,children:e},e))})]})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Date de la dépense *",type:"date",value:b.dateDepense,onChange:e=>w({...b,dateDepense:e.target.value}),InputLabelProps:{shrink:!0}})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:3,value:b.notes,onChange:e=>w({...b,notes:e.target.value})})})]})]}),a.jsxs(Le,{children:[!v&&a.jsx(Xe,{control:a.jsx(Je,{checked:N,onChange:e=>L(e.target.checked)}),label:"Imprimer reçu",sx:{mr:"auto"}}),z&&a.jsxs(i,{display:"flex",alignItems:"center",gap:1,mr:2,children:[a.jsx(qe,{size:20}),a.jsx(o,{variant:"body2",children:"Génération du reçu..."})]}),a.jsx(I,{onClick:Z,children:"Annuler"}),a.jsx(I,{onClick:ie,variant:"contained",disabled:z,children:v?"Mettre à jour":"Créer"})]})]}),a.jsx(ar,{open:V,onClose:()=>B(!1),receiptData:_,onPrintSuccess:()=>{k("Reçu imprimé avec succès"),setTimeout(()=>k(""),3e3)}})]})}catch(fr){return console.error("ExpensesPage error:",fr),a.jsxs(i,{p:3,children:[a.jsx(o,{variant:"h4",gutterBottom:!0,children:"Dépenses"}),a.jsx(O,{severity:"error",children:"Une erreur s'est produite lors du chargement de la page des dépenses. Veuillez recharger l'application ou contacter le support technique."})]})}};function lr(e){const{children:t,value:n,index:r,...s}=e;return a.jsx("div",{role:"tabpanel",hidden:n!==r,id:`reports-tabpanel-${r}`,"aria-labelledby":`reports-tab-${r}`,...s,children:n===r&&a.jsx(i,{sx:{p:3},children:t})})}const cr=()=>{const[e,t]=Ct.useState(0),[n,r]=Ct.useState("this_month"),[s,l]=Ct.useState(""),[d,u]=Ct.useState(""),[m,p]=Ct.useState([]),[x,g]=Ct.useState([]),[y,j]=Ct.useState([]),[C,b]=Ct.useState([]),[E,P]=Ct.useState([]),[F,U]=Ct.useState([]),[T,k]=Ct.useState({tauxChangeUSDCDF:2800}),[R,M]=Ct.useState(""),[N,L]=Ct.useState("");Ct.useEffect(()=>{(async()=>{try{const e=await mi.getSettings();k(e)}catch(e){console.error("Error loading settings:",e)}})()},[]),Ct.useEffect(()=>{V()},[]),Ct.useEffect(()=>{q()},[m,C,n,s,d]);const V=async()=>{try{console.log("ReportsPage: Loading data..."),p(await mi.getSales()),g(await mi.getProducts()),j(await mi.getDebts()),b(await mi.getExpenses()),console.log("ReportsPage: Data loaded successfully")}catch(e){console.error("ReportsPage: Error loading data:",e),p([]),g([]),j([]),b([])}},q=()=>{const e=new Date;let t,a;switch(n){case"today":t=Tt(e),a=kt(e);break;case"this_week":t=Rt(e,7),a=e;break;case"this_month":default:t=It(e),a=Nt(e);break;case"last_month":const n=new Date(e.getFullYear(),e.getMonth()-1,1);t=It(n),a=Nt(n);break;case"custom":s&&d?(t=new Date(s),a=new Date(d)):(t=It(e),a=Nt(e))}const i=m.filter(e=>{const n=new Date(e.datevente);return Ot(n,{start:t,end:a})}),r=C.filter(e=>{const n=new Date(e.dateDepense);return Ot(n,{start:t,end:a})});P(i),U(r)},B=(e,t)=>"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`,_=e=>{try{const t=null==e||isNaN(e)?0:e,n=t/((null==T?void 0:T.tauxChangeUSDCDF)||2800);return{primary:`${t.toLocaleString("fr-FR")} CDF`,secondary:`$${n.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})}`}}catch(t){return console.error("ReportsPage: Error formatting dual currency:",t),{primary:"0 CDF",secondary:"$0.00"}}},$=E.reduce((e,t)=>e+t.totalCDF,0),z=E.length,W=z>0?$/z:0,X=F.reduce((e,t)=>e+t.montantCDF,0),Q=$-X,H=E.reduce((e,t)=>(t.produits.forEach(t=>{e[t.produitId]||(e[t.produitId]={nom:t.nomProduit,quantite:0,revenue:0}),e[t.produitId].quantite+=t.quantite,e[t.produitId].revenue+=t.totalCDF}),e),{}),G=Object.values(H).sort((e,t)=>t.revenue-e.revenue).slice(0,10),Y=E.reduce((e,t)=>(e[t.methodePaiement]=(e[t.methodePaiement]||0)+t.totalCDF,e),{}),K=E.reduce((e,t)=>(t.produits.forEach(t=>{const n=x.find(e=>e.id===t.produitId);n&&(e[n.categorie]=(e[n.categorie]||0)+t.totalCDF)}),e),{}),Z=E.reduce((e,t)=>{const n=Ut(new Date(t.datevente),"yyyy-MM-dd");return e[n]=(e[n]||0)+t.totalCDF,e},{}),ae=Array.from({length:30},(e,t)=>{const n=Rt(new Date,29-t),a=Ut(n,"yyyy-MM-dd");return{date:Ut(n,"dd/MM"),revenue:Z[a]||0}}),ie={labels:ae.map(e=>e.date),datasets:[{label:"Profit (USD)",data:ae.map(e=>e.revenue),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1}]},re={labels:["Cash","Banque","Mobile Money"],datasets:[{data:[Y.cash||0,Y.banque||0,Y.mobile_money||0],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]},se={labels:Object.keys(K),datasets:[{label:"Profit par catégorie (USD)",data:Object.values(K),backgroundColor:["#FF6384","#36A2EB","#FFCE56","#4BC0C0","#9966FF","#FF9F40"]}]};return a.jsxs(i,{children:[a.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[a.jsx(o,{variant:"h4",children:"Rapports et Analyses"}),a.jsx(I,{variant:"contained",startIcon:a.jsx(Ue,{}),onClick:()=>{try{const e=(null==T?void 0:T.tauxChangeUSDCDF)||2800,t=(e,t)=>{const n=null==e||isNaN(e)?0:e;return"USD"===t?`$${Ri(n,2,2)}`:`${Ri(n,0,0)} CDF`},a=[{label:"Période",value:"custom"===n?`${s} - ${d}`:n},{label:"Date de Génération",value:Ut(new Date,"dd/MM/yyyy HH:mm")},{label:"Total Ventes",value:z.toString()},{label:"Chiffre d'Affaires (CDF)",value:t($,"CDF")},{label:"Chiffre d'Affaires (USD)",value:t($/e,"USD")},{label:"Total Dépenses (CDF)",value:t(X,"CDF")},{label:"Total Dépenses (USD)",value:t(X/e,"USD")},{label:"Bénéfice Net (CDF)",value:t(Q,"CDF")},{label:"Bénéfice Net (USD)",value:t(Q/e,"USD")},{label:"Vente Moyenne (CDF)",value:t(W,"CDF")},{label:"Vente Moyenne (USD)",value:t(W/e,"USD")}],i=G.map((n,a)=>({rang:a+1,produit:n.nom||"N/A",quantiteVendue:n.quantite||0,revenuesCDF:t(n.revenue||0,"CDF"),revenuesUSD:t((n.revenue||0)/e,"USD"),revenuMoyenCDF:t((n.revenue||0)/(n.quantite||1),"CDF"),revenuMoyenUSD:t((n.revenue||0)/(n.quantite||1)/e,"USD")})),r=new Date;let o,l;switch(n){case"today":o=Tt(r),l=kt(r);break;case"this_week":o=Rt(r,7),l=r;break;case"this_month":default:o=It(r),l=Nt(r);break;case"last_month":const e=new Date(r.getFullYear(),r.getMonth()-1,1);o=It(e),l=Nt(e);break;case"custom":s&&d?(o=new Date(s),l=new Date(d)):(o=It(r),l=Nt(r))}const c=Vt({start:o,end:l}).map(n=>{const a=Ut(n,"yyyy-MM-dd"),i=E.filter(e=>Ut(new Date(e.datevente),"yyyy-MM-dd")===a).reduce((e,t)=>e+t.totalCDF,0);return{date:Ut(n,"dd/MM/yyyy"),revenueCDF:t(i,"CDF"),revenueUSD:t(i/e,"USD")}}),u=qt({start:o,end:l},{weekStartsOn:1}).map(n=>{const a=Bt(n,{weekStartsOn:1}),i=E.filter(e=>{const t=new Date(e.datevente);return Ot(t,{start:n,end:a})}).reduce((e,t)=>e+t.totalCDF,0);return{semaine:`${Ut(n,"dd/MM")} - ${Ut(a,"dd/MM/yyyy")}`,revenueCDF:t(i,"CDF"),revenueUSD:t(i/e,"USD")}}),m=_t({start:o,end:l}).map(n=>{const a=Nt(n),i=E.filter(e=>{const t=new Date(e.datevente);return Ot(t,{start:n,end:a})}).reduce((e,t)=>e+t.totalCDF,0);return{mois:Ut(n,"MMMM yyyy"),revenueCDF:t(i,"CDF"),revenueUSD:t(i/e,"USD")}}),h=y.filter(e=>"paid"!==e.statut),p=h.reduce((e,t)=>e+t.montantRestantCDF,0),g={nombreDettes:h.length,montantTotalCDF:t(p,"CDF"),montantTotalUSD:t(p/e,"USD")},j=Object.entries(K).map(([n,a])=>{const i=E.reduce((e,t)=>e+t.produits.filter(e=>{const t=x.find(t=>t.id===e.produitId);return t&&t.categorie===n}).length,0);return{categorie:n||"Non catégorisé",revenuesCDF:t(a,"CDF"),revenuesUSD:t(a/e,"USD"),nombreVentes:i}});let D="SmartBoutique - Rapport d'Analyse\n\n";D+="RÉSUMÉ DU RAPPORT\n",D+="Métrique,Valeur\n",a.forEach(e=>{D+=`"${e.label}","${e.value}"\n`}),D+="\n\nTOP PRODUITS LES PLUS VENDUS\n",D+="Rang,Produit,Quantité Vendue,Revenus (CDF),Revenus (USD),Revenu Moyen (CDF),Revenu Moyen (USD)\n",i.forEach(e=>{D+=`${e.rang},"${e.produit}",${e.quantiteVendue},"${e.revenuesCDF}","${e.revenuesUSD}","${e.revenuMoyenCDF}","${e.revenuMoyenUSD}"\n`}),D+="\n\nREVENU JOURNALIER\n",D+="Date,Revenus (CDF),Revenus (USD)\n",c.forEach(e=>{D+=`"${e.date}","${e.revenueCDF}","${e.revenueUSD}"\n`}),D+="\n\nREVENU PAR SEMAINE\n",D+="Semaine,Revenus (CDF),Revenus (USD)\n",u.forEach(e=>{D+=`"${e.semaine}","${e.revenueCDF}","${e.revenueUSD}"\n`}),D+="\n\nREVENU PAR MOIS\n",D+="Mois,Revenus (CDF),Revenus (USD)\n",m.forEach(e=>{D+=`"${e.mois}","${e.revenueCDF}","${e.revenueUSD}"\n`}),D+="\n\nDETTES DUES\n",D+="Métrique,Valeur\n",D+=`"Nombre de Dettes Impayées","${g.nombreDettes}"\n`,D+=`"Montant Total (CDF)","${g.montantTotalCDF}"\n`,D+=`"Montant Total (USD)","${g.montantTotalUSD}"\n`,D+="\n\nPERFORMANCE PAR CATÉGORIE\n",D+="Catégorie,Revenus (CDF),Revenus (USD),Nombre de Ventes\n",j.forEach(e=>{D+=`"${e.categorie}","${e.revenuesCDF}","${e.revenuesUSD}",${e.nombreVentes}\n`});const S=`rapport_${Ut(new Date,"yyyy-MM-dd_HH-mm")}.csv`,v=new Blob(["\ufeff"+D],{type:"text/csv;charset=utf-8"}),C=URL.createObjectURL(v),b=document.createElement("a");b.href=C,b.download=S,document.body.appendChild(b),b.click(),document.body.removeChild(b),URL.revokeObjectURL(C),M("Rapport exporté avec succès (compatible Excel)"),setTimeout(()=>M(""),3e3)}catch(e){console.error("Erreur lors de l'exportation du rapport:",e),L("Erreur lors de l'exportation du rapport. Veuillez réessayer."),setTimeout(()=>L(""),5e3)}},children:"Exporter le Rapport"})]}),R&&a.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>M(""),children:R}),N&&a.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>L(""),children:N}),a.jsx(c,{sx:{p:2,mb:3},children:a.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[a.jsx(ce,{item:!0,xs:12,md:4,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Période"}),a.jsxs(Se,{value:n,label:"Période",onChange:e=>r(e.target.value),children:[a.jsx(J,{value:"today",children:"Aujourd'hui"}),a.jsx(J,{value:"this_week",children:"Cette semaine"}),a.jsx(J,{value:"this_month",children:"Ce mois"}),a.jsx(J,{value:"last_month",children:"Mois dernier"}),a.jsx(J,{value:"custom",children:"Personnalisée"})]})]})}),"custom"===n&&a.jsxs(a.Fragment,{children:[a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsx(ne,{fullWidth:!0,label:"Date de début",type:"date",value:s,onChange:e=>l(e.target.value),InputLabelProps:{shrink:!0}})}),a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsx(ne,{fullWidth:!0,label:"Date de fin",type:"date",value:d,onChange:e=>u(e.target.value),InputLabelProps:{shrink:!0}})})]})]})}),a.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Chiffre d'Affaires"}),a.jsx(o,{variant:"h6",color:"primary",fontWeight:"medium",children:_($*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",_($*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),a.jsx(ve,{color:"primary",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Nombre de Ventes"}),a.jsx(o,{variant:"h6",color:"success.main",children:z})]}),a.jsx(S,{color:"success",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Dépenses"}),a.jsx(o,{variant:"h6",color:"error",fontWeight:"medium",children:_(X*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",_(X*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),a.jsx(f,{color:"error",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Profit Après Dépenses"}),a.jsx(o,{variant:"h6",color:Q>=0?"success.main":"error",fontWeight:"medium",children:_(Q*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",_(Q*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),a.jsx(v,{color:Q>=0?"success":"error",sx:{fontSize:40}})]})})})})]}),a.jsx(c,{sx:{mb:3},children:a.jsxs(it,{value:e,onChange:(e,n)=>{t(n)},"aria-label":"reports tabs",children:[a.jsx(rt,{label:"Tendances",icon:a.jsx(ve,{})}),a.jsx(rt,{label:"Produits",icon:a.jsx(D,{})}),a.jsx(rt,{label:"Analyses",icon:a.jsx(w,{})})]})}),a.jsx(lr,{value:e,index:0,children:a.jsxs(ce,{container:!0,spacing:3,children:[a.jsx(ce,{item:!0,xs:12,md:8,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Évolution des Ventes (30 derniers jours)"}),a.jsx(te,{children:a.jsx($t,{data:ie,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})})]})}),a.jsx(ce,{item:!0,xs:12,md:4,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Méthodes de Paiement"}),a.jsx(te,{children:a.jsx(zt,{data:re,options:{responsive:!0,plugins:{legend:{position:"bottom"}}}})})]})}),a.jsx(ce,{item:!0,xs:12,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Performance par Catégorie"}),a.jsx(te,{children:a.jsx(Wt,{data:se,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})})]})})]})}),a.jsx(lr,{value:e,index:1,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Top 10 Produits les Plus Vendus"}),a.jsx(te,{children:a.jsx(me,{children:a.jsxs(he,{children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:"Rang"}),a.jsx(ge,{children:"Produit"}),a.jsx(ge,{align:"right",children:"Quantité Vendue"}),a.jsx(ge,{align:"right",children:"Profit"}),a.jsx(ge,{align:"right",children:"Profit Moyen"})]})}),a.jsx(ye,{children:G.map((e,t)=>a.jsxs(xe,{children:[a.jsx(ge,{children:a.jsx(A,{label:`#${t+1}`,color:t<3?"primary":"default",size:"small"})}),a.jsx(ge,{children:e.nom}),a.jsx(ge,{align:"right",children:e.quantite}),a.jsx(ge,{align:"right",children:B(e.revenue,"USD")}),a.jsx(ge,{align:"right",children:B(e.revenue/e.quantite,"USD")})]},t))})]})})})]})}),a.jsx(lr,{value:e,index:2,children:a.jsxs(ce,{container:!0,spacing:3,children:[a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Métriques de Performance"}),a.jsx(te,{children:a.jsxs(ce,{container:!0,spacing:2,children:[a.jsxs(ce,{item:!0,xs:6,children:[a.jsx(o,{variant:"body2",color:"text.secondary",children:"Vente Moyenne"}),a.jsx(o,{variant:"h6",children:B(W,"USD")})]}),a.jsxs(ce,{item:!0,xs:6,children:[a.jsx(o,{variant:"body2",color:"text.secondary",children:"Produits Vendus"}),a.jsx(o,{variant:"h6",children:E.reduce((e,t)=>e+t.produits.reduce((e,t)=>e+t.quantite,0),0)})]}),a.jsxs(ce,{item:!0,xs:6,children:[a.jsx(o,{variant:"body2",color:"text.secondary",children:"Panier Moyen"}),a.jsxs(o,{variant:"h6",children:[z>0?(E.reduce((e,t)=>e+t.produits.length,0)/z).toFixed(1):"0"," articles"]})]})]})})]})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Répartition des Profits"}),a.jsx(te,{children:a.jsxs(ce,{container:!0,spacing:2,children:[a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"body2",color:"text.secondary",children:"Ventes Cash"}),a.jsx(o,{variant:"h6",color:"success.main",children:B(E.filter(e=>"cash"===e.typeVente).reduce((e,t)=>e+t.totalCDF,0),"CDF")})]}),a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"body2",color:"text.secondary",children:"Ventes à Crédit"}),a.jsx(o,{variant:"h6",color:"warning.main",children:B(E.filter(e=>"credit"===e.typeVente).reduce((e,t)=>e+t.totalCDF,0),"CDF")})]}),a.jsx(h,{sx:{width:"100%",my:1}}),a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(o,{variant:"body2",color:"text.secondary",children:"Dettes Impayées"}),a.jsx(o,{variant:"h6",color:"error",children:B(y.filter(e=>"paid"!==e.statut).reduce((e,t)=>e+t.montantRestantCDF,0),"CDF")})]})]})})]})}),a.jsx(ce,{item:!0,xs:12,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Analyse des Dépenses par Catégorie"}),a.jsx(te,{children:a.jsx(me,{children:a.jsxs(he,{children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:"Catégorie"}),a.jsx(ge,{align:"right",children:"Nombre"}),a.jsx(ge,{align:"right",children:"Montant Total"}),a.jsx(ge,{align:"right",children:"Montant Moyen"}),a.jsx(ge,{align:"right",children:"% du Total"})]})}),a.jsx(ye,{children:Object.entries(F.reduce((e,t)=>(e[t.categorie]||(e[t.categorie]={count:0,total:0}),e[t.categorie].count+=1,e[t.categorie].total+=t.montantCDF,e),{})).sort(([,e],[,t])=>t.total-e.total).map(([e,t])=>a.jsxs(xe,{children:[a.jsx(ge,{children:e}),a.jsx(ge,{align:"right",children:t.count}),a.jsx(ge,{align:"right",children:B(t.total,"USD")}),a.jsx(ge,{align:"right",children:B(t.total/t.count,"USD")}),a.jsx(ge,{align:"right",children:X>0?`${(t.total/X*100).toFixed(1)}%`:"0%"})]},e))})]})})})]})})]})})]})},dr=()=>{const[e,t]=Ct.useState([]),[n,r]=Ct.useState([]),[s,d]=Ct.useState(""),[u,m]=Ct.useState(""),[h,p]=Ct.useState(""),[x,g]=Ct.useState(0),[y,j]=Ct.useState(10),[D,S]=Ct.useState(!1),[v,C]=Ct.useState(null),[b,f]=Ct.useState(!1),[w,P]=Ct.useState({nom:"",email:"",role:"employee",motDePasse:"",actif:!0}),[T,k]=Ct.useState(""),[R,M]=Ct.useState(""),N=hi.getUserPermissions(),L=hi.getCurrentUser();Ct.useEffect(()=>{V()},[]),Ct.useEffect(()=>{B()},[e,s,u,h]),Ct.useEffect(()=>{-1===y&&j(n.length||1)},[n.length,y]);const V=async()=>{const e=await mi.getUsers();t(e)},B=()=>{let t=e;if(s&&(t=t.filter(e=>e.nom.toLowerCase().includes(s.toLowerCase())||e.email.toLowerCase().includes(s.toLowerCase()))),u&&(t=t.filter(e=>e.role===u)),h){const e="active"===h;t=t.filter(t=>t.actif===e)}r(t)},_=e=>{switch(e){case"super_admin":return"Super Admin";case"admin":return"Administrateur";case"employee":return"Employé";default:return e}},$=e=>{switch(e){case"super_admin":return"error";case"admin":return"warning";case"employee":return"primary";default:return"default"}},z=e=>{switch(e){case"super_admin":case"admin":return a.jsx(ot,{});default:return a.jsx(lt,{})}},W=e=>{e?(C(e),P({nom:e.nom,email:e.email,role:e.role,motDePasse:"",actif:e.actif})):(C(null),P({nom:"",email:"",role:"employee",motDePasse:"",actif:!0})),S(!0),f(!1),k(""),M("")},X=()=>{S(!1),C(null),k(""),M("")},Q=e.length,H=e.filter(e=>e.actif).length,G=e.filter(e=>"admin"===e.role||"super_admin"===e.role).length,K=e.filter(e=>"employee"===e.role).length;return a.jsxs(i,{children:[a.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[a.jsx(o,{variant:"h4",children:"Gestion des Utilisateurs"}),N.canManageUsers&&a.jsx(I,{variant:"contained",startIcon:a.jsx(Pe,{}),onClick:()=>W(),children:"Nouvel Utilisateur"})]}),R&&a.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>M(""),children:R}),T&&a.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>k(""),children:T}),a.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Utilisateurs"}),a.jsx(o,{variant:"h6",children:Q})]}),a.jsx(E,{color:"primary",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Utilisateurs Actifs"}),a.jsx(o,{variant:"h6",color:"success.main",children:H})]}),a.jsx(st,{color:"success",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Administrateurs"}),a.jsx(o,{variant:"h6",color:"warning.main",children:G})]}),a.jsx(ot,{color:"warning",sx:{fontSize:40}})]})})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsx(te,{children:a.jsxs(i,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[a.jsxs(i,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Employés"}),a.jsx(o,{variant:"h6",color:"info.main",children:K})]}),a.jsx(lt,{color:"info",sx:{fontSize:40}})]})})})})]}),a.jsx(c,{sx:{p:2,mb:3},children:a.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[a.jsx(ce,{item:!0,xs:12,md:4,children:a.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par nom ou email...",value:s,onChange:e=>d(e.target.value),InputProps:{startAdornment:a.jsx(ae,{position:"start",children:a.jsx(Fe,{})})}})}),a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Rôle"}),a.jsxs(Se,{value:u,label:"Rôle",onChange:e=>m(e.target.value),children:[a.jsx(J,{value:"",children:"Tous les rôles"}),a.jsx(J,{value:"super_admin",children:"Super Admin"}),a.jsx(J,{value:"admin",children:"Administrateur"}),a.jsx(J,{value:"employee",children:"Employé"})]})]})}),a.jsx(ce,{item:!0,xs:12,md:3,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Statut"}),a.jsxs(Se,{value:h,label:"Statut",onChange:e=>p(e.target.value),children:[a.jsx(J,{value:"",children:"Tous les statuts"}),a.jsx(J,{value:"active",children:"Actif"}),a.jsx(J,{value:"inactive",children:"Inactif"})]})]})})]})}),a.jsxs(me,{component:c,children:[a.jsxs(he,{children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:"Utilisateur"}),a.jsx(ge,{children:"Email"}),a.jsx(ge,{align:"center",children:"Rôle"}),a.jsx(ge,{align:"center",children:"Statut"}),a.jsx(ge,{children:"Date de Création"}),N.canManageUsers&&a.jsx(ge,{align:"center",children:"Actions"})]})}),a.jsx(ye,{children:(-1===y?n:n.slice(x*y,x*y+y)).map(n=>a.jsxs(xe,{hover:!0,onClick:()=>W(n),sx:{cursor:"pointer"},children:[a.jsx(ge,{children:a.jsxs(i,{display:"flex",alignItems:"center",gap:2,children:[a.jsx(l,{sx:{bgcolor:$(n.role)},children:n.nom.charAt(0).toUpperCase()}),a.jsxs(i,{children:[a.jsx(o,{variant:"subtitle2",children:n.nom}),n.id===(null==L?void 0:L.id)&&a.jsx(A,{label:"Vous",size:"small",color:"primary"})]})]})}),a.jsx(ge,{children:n.email}),a.jsx(ge,{align:"center",children:a.jsx(A,{icon:z(n.role),label:_(n.role),color:$(n.role),size:"small"})}),a.jsx(ge,{align:"center",children:a.jsx(A,{label:n.actif?"Actif":"Inactif",color:n.actif?"success":"error",size:"small"})}),a.jsx(ge,{children:Ut(new Date(n.dateCreation),"dd/MM/yyyy",{locale:Di})}),N.canManageUsers&&a.jsx(ge,{align:"center",children:a.jsxs(i,{display:"flex",gap:1,children:[a.jsx(F,{title:"Modifier",children:a.jsx(U,{size:"small",onClick:()=>W(n),disabled:"super_admin"===n.role&&!hi.hasRole(["super_admin"]),children:a.jsx(Re,{fontSize:"small"})})}),a.jsx(F,{title:n.actif?"Désactiver":"Activer",children:a.jsx(U,{size:"small",onClick:()=>(async n=>{if(n.id===(null==L?void 0:L.id)&&n.actif)return k("Vous ne pouvez pas désactiver votre propre compte"),void setTimeout(()=>k(""),3e3);const a={...n,actif:!n.actif},i=e.map(e=>e.id===n.id?a:e);t(i),await mi.setUsers(i),M(`Utilisateur ${a.actif?"activé":"désactivé"} avec succès`),setTimeout(()=>M(""),3e3)})(n),disabled:n.id===(null==L?void 0:L.id)&&n.actif,children:n.actif?a.jsx(Y,{fontSize:"small"}):a.jsx(st,{fontSize:"small"})})}),hi.hasRole(["super_admin"])&&a.jsx(F,{title:"Supprimer",children:a.jsx(U,{size:"small",color:"error",onClick:()=>(async n=>{if(n.id===(null==L?void 0:L.id))return k("Vous ne pouvez pas supprimer votre propre compte"),void setTimeout(()=>k(""),3e3);if("super_admin"===n.role&&!hi.hasRole(["super_admin"]))return k("Seul un Super Admin peut supprimer un compte Super Admin"),void setTimeout(()=>k(""),3e3);if(window.confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur "${n.nom}" ?`)){const a=e.filter(e=>e.id!==n.id);t(a),await mi.setUsers(a),M("Utilisateur supprimé avec succès"),setTimeout(()=>M(""),3e3)}})(n),disabled:n.id===(null==L?void 0:L.id),children:a.jsx(q,{fontSize:"small"})})})]})})]},n.id))})]}),a.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:n.length,rowsPerPage:-1===y?n.length:y,page:-1===y?0:x,onPageChange:(e,t)=>{-1!==y&&g(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);j(t),g(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===y?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),a.jsxs(Ae,{open:D,onClose:X,maxWidth:"sm",fullWidth:!0,children:[a.jsx(Ie,{children:v?"Modifier l'Utilisateur":"Nouvel Utilisateur"}),a.jsxs(Ne,{children:[T&&a.jsx(O,{severity:"error",sx:{mb:2},children:T}),R&&a.jsx(O,{severity:"success",sx:{mb:2},children:R}),a.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Nom complet *",value:w.nom,onChange:e=>P({...w,nom:e.target.value})})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Email *",type:"email",value:w.email,onChange:e=>P({...w,email:e.target.value})})}),a.jsx(ce,{item:!0,xs:12,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Rôle *"}),a.jsxs(Se,{value:w.role,label:"Rôle *",onChange:e=>P({...w,role:e.target.value}),disabled:!hi.hasRole(["super_admin"])||!(!v||v.id!==(null==L?void 0:L.id)),children:[a.jsx(J,{value:"employee",children:"Employé"}),a.jsx(J,{value:"admin",children:"Administrateur"}),hi.hasRole(["super_admin"])&&a.jsx(J,{value:"super_admin",children:"Super Admin"})]})]})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:v?"Nouveau mot de passe (optionnel)":"Mot de passe *",type:b?"text":"password",value:w.motDePasse,onChange:e=>P({...w,motDePasse:e.target.value}),InputProps:{endAdornment:a.jsx(ae,{position:"end",children:a.jsx(U,{onClick:()=>f(!b),edge:"end",children:b?a.jsx(re,{}):a.jsx(se,{})})})},helperText:v?"Laissez vide pour conserver le mot de passe actuel":"Minimum 6 caractères"})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(Xe,{control:a.jsx(Ke,{checked:w.actif,onChange:e=>P({...w,actif:e.target.checked}),disabled:!(!v||v.id!==(null==L?void 0:L.id))}),label:"Compte actif"})})]})]}),a.jsxs(Le,{children:[a.jsx(I,{onClick:X,children:"Annuler"}),a.jsx(I,{onClick:async()=>{if(!w.nom.trim())return void k("Le nom est requis");if(!w.email.trim())return void k("L'email est requis");if(!v&&!w.motDePasse.trim())return void k("Le mot de passe est requis pour un nouvel utilisateur");if(w.motDePasse&&w.motDePasse.length<6)return void k("Le mot de passe doit contenir au moins 6 caractères");if(e.some(e=>e.email.toLowerCase()===w.email.trim().toLowerCase()&&e.id!==(null==v?void 0:v.id)))return void k("Un utilisateur avec cet email existe déjà");if("super_admin"===w.role&&!hi.hasRole(["super_admin"]))return void k("Seul un Super Admin peut créer ou modifier un compte Super Admin");if(v&&v.id===(null==L?void 0:L.id)&&w.role!==v.role)return void k("Vous ne pouvez pas modifier votre propre rôle");if(v&&v.id===(null==L?void 0:L.id)&&!w.actif)return void k("Vous ne pouvez pas désactiver votre propre compte");const n=(new Date).toISOString();if(v){const n={...v,nom:w.nom.trim(),email:w.email.trim(),role:w.role,actif:w.actif,...w.motDePasse&&{motDePasse:w.motDePasse}},a=e.map(e=>e.id===v.id?n:e);t(a),await mi.setUsers(a),v.id===(null==L?void 0:L.id)&&await mi.setCurrentUser(n),M("Utilisateur mis à jour avec succès")}else{const a={id:Date.now().toString(),nom:w.nom.trim(),email:w.email.trim(),role:w.role,motDePasse:w.motDePasse,dateCreation:n,actif:w.actif},i=[...e,a];t(i),await mi.setUsers(i),M("Utilisateur créé avec succès")}setTimeout(()=>{X()},1500)},variant:"contained",children:v?"Mettre à jour":"Créer"})]})]})]})};class ur{async exportAllData(){try{const[e,t,n,a,i,r]=await Promise.all([mi.getProducts(),mi.getUsers(),mi.getSales(),mi.getDebts(),mi.getExpenses(),mi.getSettings()]),s={exportDate:(new Date).toISOString(),products:Oa.arrayToCSV(e,Va),users:Oa.arrayToCSV(t,qa),sales:Oa.arrayToCSV(n,Ba),debts:Oa.arrayToCSV(a,_a),expenses:Oa.arrayToCSV(i,$a),settings:Oa.arrayToCSV(Qa(r),Xa)},o=`SmartBoutique - Sauvegarde Complète\nDate d'exportation: ${s.exportDate}\n\n=== PRODUITS ===\n${s.products}\n\n=== UTILISATEURS ===\n${s.users}\n\n=== VENTES ===\n${s.sales}\n\n=== DETTES ===\n${s.debts}\n\n=== DÉPENSES ===\n${s.expenses}\n\n=== PARAMÈTRES ===\n${s.settings}\n`;return{success:!0,message:"Exportation complète réussie (compatible Excel)",data:"\ufeff"+o}}catch(fr){return console.error("Erreur lors de l'exportation complète:",fr),{success:!1,message:"Erreur lors de l'exportation: "+fr.message}}}async exportData(e){try{let t=[],n=[],a="";switch(e){case"products":t=await mi.getProducts(),n=Va,a="produits";break;case"users":t=await mi.getUsers(),n=qa,a="utilisateurs";break;case"sales":t=await mi.getSales(),n=Ba,a="ventes";break;case"debts":t=await mi.getDebts(),n=_a,a="dettes";break;case"expenses":t=await mi.getExpenses(),n=$a,a="depenses"}const i=Oa.arrayToCSV(t,n);return{success:!0,message:`Exportation ${a} réussie (${t.length} enregistrements)`,data:i}}catch(fr){return console.error(`Erreur lors de l'exportation ${e}:`,fr),{success:!1,message:"Erreur lors de l'exportation: "+fr.message}}}async importProducts(e,t=!1){try{const n=Oa.csvToArray(e,Va),a=Oa.validateCSVData(n,Va);if(!a.isValid)return{success:!1,message:"Données invalides détectées",errors:a.errors,importedCount:0};let i=n;if(!t){const e=await mi.getProducts(),t=new Set(e.map(e=>e.id)),a=n.filter(e=>!t.has(e.id));i=[...e,...a]}return await mi.setProducts(i),{success:!0,message:`${n.length} produits importés avec succès`,errors:[],importedCount:n.length}}catch(fr){return console.error("Erreur lors de l'importation des produits:",fr),{success:!1,message:"Erreur lors de l'importation: "+fr.message,errors:[fr.message],importedCount:0}}}async importUsers(e,t=!1){try{const n=Oa.csvToArray(e,qa),a=Oa.validateCSVData(n,qa);if(!a.isValid)return{success:!1,message:"Données invalides détectées",errors:a.errors,importedCount:0};let i=n;if(!t){const e=await mi.getUsers(),t=new Set(e.map(e=>e.id)),a=n.filter(e=>!t.has(e.id));i=[...e,...a]}return await mi.setUsers(i),{success:!0,message:`${n.length} utilisateurs importés avec succès`,errors:[],importedCount:n.length}}catch(fr){return console.error("Erreur lors de l'importation des utilisateurs:",fr),{success:!1,message:"Erreur lors de l'importation: "+fr.message,errors:[fr.message],importedCount:0}}}generateTemplate(e){switch(e){case"products":return Oa.generateTemplate(Va);case"users":return Oa.generateTemplate(qa);case"sales":return Oa.generateTemplate(Ba);case"debts":return Oa.generateTemplate(_a);case"expenses":return Oa.generateTemplate($a);default:return""}}async createAutomaticBackup(){try{const e=await this.exportAllData();if(e.success&&e.data){const t=(new Date).toISOString().replace(/[:.]/g,"-");return{success:!0,message:`Sauvegarde automatique créée: ${t}`,data:e.data}}return e}catch(fr){return console.error("Erreur lors de la sauvegarde automatique:",fr),{success:!1,message:"Erreur lors de la sauvegarde automatique: "+fr.message}}}getSampleCSVData(e){switch(e){case"products":return"ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification\nSAMPLE1,Produit Exemple,Description du produit exemple,5600,2,SAMPLE123,Alimentation,50,10,1234567890,2024-01-01,2024-01-01";case"users":return"ID,Nom,Email,Rôle,Mot de Passe,Date de Création,Actif\nSAMPLE1,Utilisateur Exemple,<EMAIL>,employee,motdepasse123,2024-01-01,Oui";default:return this.generateTemplate(e)}}}const mr=new ur,hr=Object.freeze(Object.defineProperty({__proto__:null,CSVImportExportService:ur,csvImportExportService:mr},Symbol.toStringTag,{value:"Module"})),pr=({onSuccess:e,onError:t})=>{var n,r,s,l;const[c,d]=Ct.useState(!1),[u,m]=Ct.useState(!1),[p,x]=Ct.useState(!1),[g,y]=Ct.useState("products"),[j,D]=Ct.useState(""),[S,v]=Ct.useState(!1),[C,b]=Ct.useState(""),f=[{key:"products",label:"Produits",icon:"📦"},{key:"users",label:"Utilisateurs",icon:"👥"},{key:"sales",label:"Ventes",icon:"💰"},{key:"debts",label:"Dettes",icon:"📋"},{key:"expenses",label:"Dépenses",icon:"💸"}],w=()=>{const e=mr.generateTemplate(g);D(e)};return a.jsx(ee,{children:a.jsxs(te,{children:[a.jsxs(o,{variant:"h6",gutterBottom:!0,children:[a.jsx(ct,{sx:{mr:1,verticalAlign:"middle"}}),"Gestion des Données CSV"]}),a.jsx(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Exportez et importez vos données au format CSV pour une meilleure portabilité et accessibilité."}),a.jsxs(ce,{container:!0,spacing:2,children:[a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(i,{sx:{p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[a.jsxs(o,{variant:"subtitle1",gutterBottom:!0,children:[a.jsx(Ue,{sx:{mr:1,verticalAlign:"middle"}}),"Exportation"]}),a.jsx(I,{variant:"contained",color:"primary",fullWidth:!0,onClick:async()=>{d(!0);try{const n=await mr.exportAllData();if(n.success&&n.data){const t=new Blob([n.data],{type:"text/plain;charset=utf-8"}),a=URL.createObjectURL(t),i=document.createElement("a");i.href=a,i.download=`SmartBoutique_Backup_${(new Date).toISOString().split("T")[0]}.txt`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(a),null==e||e("Sauvegarde complète exportée avec succès (compatible Excel)")}else null==t||t(n.message)}catch(fr){null==t||t("Erreur lors de l'exportation: "+fr.message)}finally{d(!1)}},disabled:c,startIcon:c?a.jsx(qe,{size:20}):a.jsx(dt,{}),sx:{mb:2},children:"Exporter Toutes les Données"}),a.jsx(h,{sx:{my:2}}),a.jsx(o,{variant:"body2",gutterBottom:!0,children:"Exporter un type de données spécifique:"}),a.jsx(i,{sx:{mb:2},children:f.map(e=>a.jsx(A,{label:`${e.icon} ${e.label}`,onClick:()=>y(e.key),color:g===e.key?"primary":"default",sx:{m:.5}},e.key))}),a.jsxs(I,{variant:"outlined",fullWidth:!0,onClick:async()=>{d(!0);try{const n=await mr.exportData(g);n.success&&n.data?(b(n.data),m(!0),null==e||e(n.message)):null==t||t(n.message)}catch(fr){null==t||t("Erreur lors de l'exportation: "+fr.message)}finally{d(!1)}},disabled:c,startIcon:a.jsx(ut,{}),children:["Exporter ",null==(n=f.find(e=>e.key===g))?void 0:n.label]})]})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(i,{sx:{p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[a.jsxs(o,{variant:"subtitle1",gutterBottom:!0,children:[a.jsx(Te,{sx:{mr:1,verticalAlign:"middle"}}),"Importation"]}),a.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Sélectionnez le type de données à importer:"}),a.jsx(i,{sx:{mb:2},children:f.filter(e=>["products","users"].includes(e.key)).map(e=>a.jsx(A,{label:`${e.icon} ${e.label}`,onClick:()=>y(e.key),color:g===e.key?"primary":"default",sx:{m:.5}},e.key))}),a.jsxs(I,{variant:"outlined",fullWidth:!0,onClick:()=>x(!0),startIcon:a.jsx(Te,{}),sx:{mb:1},children:["Importer ",null==(r=f.find(e=>e.key===g))?void 0:r.label]}),a.jsx(I,{variant:"text",size:"small",fullWidth:!0,onClick:w,children:"Obtenir un modèle CSV"})]})})]}),a.jsxs(Ae,{open:u,onClose:()=>m(!1),maxWidth:"md",fullWidth:!0,children:[a.jsxs(Ie,{children:["Données Exportées - ",null==(s=f.find(e=>e.key===g))?void 0:s.label]}),a.jsx(Ne,{children:a.jsx(ne,{multiline:!0,rows:10,fullWidth:!0,value:C,variant:"outlined",InputProps:{readOnly:!0},sx:{fontFamily:"monospace"}})}),a.jsxs(Le,{children:[a.jsx(I,{onClick:()=>m(!1),children:"Fermer"}),a.jsx(I,{onClick:()=>{var e;const t=(null==(e=f.find(e=>e.key===g))?void 0:e.label)||g;let n=C;n.startsWith("\ufeff")||(n="\ufeff"+n);const a=new Blob([n],{type:"text/csv;charset=utf-8"}),i=URL.createObjectURL(a),r=document.createElement("a");r.href=i,r.download=`SmartBoutique_${t}_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(i),m(!1)},variant:"contained",startIcon:a.jsx(Ue,{}),children:"Télécharger CSV"})]})]}),a.jsxs(Ae,{open:p,onClose:()=>x(!1),maxWidth:"md",fullWidth:!0,children:[a.jsxs(Ie,{children:["Importer ",null==(l=f.find(e=>e.key===g))?void 0:l.label]}),a.jsxs(Ne,{children:[a.jsx(O,{severity:"info",sx:{mb:2},children:'Collez le contenu CSV ci-dessous. Utilisez "Obtenir un modèle CSV" pour voir le format requis.'}),a.jsx(ne,{multiline:!0,rows:8,fullWidth:!0,value:j,onChange:e=>D(e.target.value),placeholder:"Collez votre contenu CSV ici...",variant:"outlined",sx:{mb:2,fontFamily:"monospace"}}),a.jsx(Xe,{control:a.jsx(Je,{checked:S,onChange:e=>v(e.target.checked)}),label:"Remplacer les données existantes"})]}),a.jsxs(Le,{children:[a.jsx(I,{onClick:()=>x(!1),children:"Annuler"}),a.jsx(I,{onClick:w,variant:"outlined",children:"Obtenir Modèle"}),a.jsx(I,{onClick:async()=>{if(j.trim()){d(!0);try{let n;switch(g){case"products":n=await mr.importProducts(j,S);break;case"users":n=await mr.importUsers(j,S);break;default:return void(null==t||t("Type de données non supporté pour l'importation"))}n.success?(null==e||e(n.message),x(!1),D("")):null==t||t(n.message+"\n"+n.errors.join("\n"))}catch(fr){null==t||t("Erreur lors de l'importation: "+fr.message)}finally{d(!1)}}else null==t||t("Veuillez saisir le contenu CSV à importer")},variant:"contained",disabled:c||!j.trim(),startIcon:c?a.jsx(qe,{size:20}):a.jsx(Te,{}),children:"Importer"})]})]})]})})},xr=({value:e="",onChange:t,disabled:n=!1,maxSizeKB:r=500,acceptedFormats:s=["image/jpeg","image/jpg","image/png","image/gif"]})=>{const[l,c]=Ct.useState(""),[d,u]=Ct.useState(!1),m=Ct.useRef(null),h=()=>{m.current&&m.current.click()};return a.jsxs(i,{children:[a.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Logo de l'entreprise"}),a.jsx("input",{ref:m,type:"file",accept:s.join(","),onChange:e=>{var n;const a=null==(n=e.target.files)?void 0:n[0];if(!a)return;if(c(""),u(!0),!s.includes(a.type))return c(`Format non supporté. Formats acceptés: ${s.map(e=>e.split("/")[1].toUpperCase()).join(", ")}`),void u(!1);if(a.size/1024>r)return c(`Fichier trop volumineux. Taille maximale: ${r}KB`),void u(!1);const i=new FileReader;i.onload=e=>{var n;const a=null==(n=e.target)?void 0:n.result;a&&t(a),u(!1)},i.onerror=()=>{c("Erreur lors de la lecture du fichier"),u(!1)},i.readAsDataURL(a)},style:{display:"none"},disabled:n}),a.jsx(ee,{variant:"outlined",sx:{mb:2},children:a.jsx(te,{sx:{textAlign:"center",py:3},children:e?a.jsxs(i,{children:[a.jsx(i,{component:"img",src:e,alt:"Logo de l'entreprise",sx:{maxWidth:"200px",maxHeight:"100px",objectFit:"contain",border:"1px solid #e0e0e0",borderRadius:1,mb:2}}),a.jsxs(i,{children:[a.jsx(I,{variant:"outlined",startIcon:a.jsx(mt,{}),onClick:h,disabled:n||d,sx:{mr:1},children:"Changer"}),a.jsx(U,{color:"error",onClick:()=>{t(""),c(""),m.current&&(m.current.value="")},disabled:n||d,title:"Supprimer le logo",children:a.jsx(q,{})})]})]}):a.jsxs(i,{children:[a.jsx(ht,{sx:{fontSize:48,color:"text.secondary",mb:2}}),a.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Aucun logo configuré"}),a.jsx(I,{variant:"contained",startIcon:d?a.jsx(qe,{size:20}):a.jsx(mt,{}),onClick:h,disabled:n||d,children:d?"Chargement...":"Télécharger un logo"})]})})}),l&&a.jsx(O,{severity:"error",sx:{mb:2},children:l}),a.jsxs(o,{variant:"caption",color:"text.secondary",children:["Formats acceptés: ",s.map(e=>e.split("/")[1].toUpperCase()).join(", ")," • Taille maximale: ",r,"KB • Recommandé: 200x100px pour un affichage optimal sur les reçus thermiques"]})]})};function gr(e){const{children:t,value:n,index:r,...s}=e;return a.jsx("div",{role:"tabpanel",hidden:n!==r,id:`settings-tabpanel-${r}`,"aria-labelledby":`settings-tab-${r}`,...s,children:n===r&&a.jsx(i,{sx:{p:3},children:t})})}const yr=()=>{const[e,t]=Ct.useState(null),[n,r]=Ct.useState(0),[s,l]=Ct.useState(""),[d,u]=Ct.useState(""),[m,g]=Ct.useState({nom:"",adresse:"",telephone:"",email:"",rccm:"",idNat:"",logo:""}),[j,D]=Ct.useState({tauxChangeUSDCDF:2800,seuilStockBas:10}),[S,v]=Ct.useState({impressionAutomatique:!1,taillePapier:"thermal"}),[C,b]=Ct.useState([]),[f,w]=Ct.useState(!1),[E,P]=Ct.useState(null),[F,T]=Ct.useState({nom:"",description:"",couleur:"#2196F3"}),k=hi.getUserPermissions();Ct.useEffect(()=>{R()},[]);const R=async()=>{var e,n,a,i,r,s,o,l,c,d,u,m,h,p,x,y;const j=await mi.getSettings();t(j),g({nom:(null==(e=null==j?void 0:j.company)?void 0:e.nom)||(null==(n=null==j?void 0:j.entreprise)?void 0:n.nom)||"SmartBoutique",adresse:(null==(a=null==j?void 0:j.company)?void 0:a.adresse)||(null==(i=null==j?void 0:j.entreprise)?void 0:i.adresse)||"Kinshasa, RDC",telephone:(null==(r=null==j?void 0:j.company)?void 0:r.telephone)||(null==(s=null==j?void 0:j.entreprise)?void 0:s.telephone)||"+*********** 000",email:(null==(o=null==j?void 0:j.company)?void 0:o.email)||(null==(l=null==j?void 0:j.entreprise)?void 0:l.email)||"<EMAIL>",rccm:(null==(c=null==j?void 0:j.company)?void 0:c.rccm)||(null==(d=null==j?void 0:j.entreprise)?void 0:d.rccm)||"",idNat:(null==(u=null==j?void 0:j.company)?void 0:u.idNat)||(null==(m=null==j?void 0:j.entreprise)?void 0:m.idNat)||"",logo:(null==(h=null==j?void 0:j.company)?void 0:h.logo)||(null==(p=null==j?void 0:j.entreprise)?void 0:p.logo)||""}),D({tauxChangeUSDCDF:(null==j?void 0:j.tauxChangeUSDCDF)||2800,seuilStockBas:(null==j?void 0:j.seuilStockBas)||10}),v({impressionAutomatique:(null==(x=null==j?void 0:j.impression)?void 0:x.impressionAutomatique)||!1,taillePapier:(null==(y=null==j?void 0:j.impression)?void 0:y.taillePapier)||"thermal"}),b((null==j?void 0:j.categories)||[{id:"1",nom:"Alimentation",couleur:"#4CAF50"},{id:"2",nom:"Vêtements",couleur:"#2196F3"},{id:"3",nom:"Électronique",couleur:"#FF9800"},{id:"4",nom:"Boissons",couleur:"#9C27B0"},{id:"5",nom:"Épicerie",couleur:"#795548"},{id:"6",nom:"Livres",couleur:"#607D8B"}])},M=e=>{e?(P(e),T({nom:e.nom,description:e.description,couleur:e.couleur})):(P(null),T({nom:"",description:"",couleur:"#2196F3"})),w(!0),u("")},N=()=>{w(!1),P(null),u("")},L=async()=>{try{console.log("🔄 Initializing essential user accounts only...");const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];await mi.setUsers(e),await mi.setProducts([]),await mi.setSales([]),await mi.setDebts([]),await mi.setExpenses([]),await mi.setEmployeePayments([]);const t={tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Alimentation",couleur:"#4CAF50"},{id:"2",nom:"Vêtements",couleur:"#2196F3"},{id:"3",nom:"Électronique",couleur:"#FF9800"},{id:"4",nom:"Boissons",couleur:"#9C27B0"},{id:"5",nom:"Épicerie",couleur:"#795548"},{id:"6",nom:"Livres",couleur:"#607D8B"}],company:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+*********** 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}};await mi.setSettings(t),console.log("✅ Essential user accounts initialized successfully (no demo data)")}catch(e){throw console.error("❌ Error initializing essential users:",e),e}},B=async()=>{try{(()=>{var e,t;return"capacitor:"===window.location.protocol||(null==(t=null==(e=window.Capacitor)?void 0:e.isNativePlatform)?void 0:t.call(e))||navigator.userAgent.includes("Capacitor")})()?await _():await $(),console.log("✅ Complete data reset performed successfully")}catch(e){throw console.error("❌ Error during data reset:",e),e}},_=async()=>{try{const{Preferences:t}=await ba(async()=>{const{Preferences:e}=await Promise.resolve().then(()=>Ga);return{Preferences:e}},void 0,import.meta.url),{keys:n}=await t.keys(),a=n.filter(e=>e.startsWith("smartboutique_")||e.startsWith("smartboutique_csv_"));for(const e of a)await t.remove({key:e});try{const{mobileSQLiteStorageService:e}=await ba(async()=>{const{mobileSQLiteStorageService:e}=await Promise.resolve().then(()=>di);return{mobileSQLiteStorageService:e}},void 0,import.meta.url);console.log("Mobile SQLite data cleared (if available)")}catch(e){console.log("Mobile SQLite not available or already cleared")}console.log("✅ Mobile data cleared successfully")}catch(e){throw console.error("❌ Error clearing mobile data:",e),e}},$=async()=>{try{Object.keys(localStorage).forEach(e=>{e.startsWith("smartboutique_")&&localStorage.removeItem(e)});try{const{sqliteStorageService:e}=await ba(async()=>{const{sqliteStorageService:e}=await Promise.resolve().then(()=>ii);return{sqliteStorageService:e}},void 0,import.meta.url);console.log("Desktop SQLite data cleared (if available)")}catch(e){console.log("Desktop SQLite not available or already cleared")}console.log("✅ Desktop data cleared successfully")}catch(e){throw console.error("❌ Error clearing desktop data:",e),e}};return e?a.jsxs(i,{children:[a.jsx(o,{variant:"h4",gutterBottom:!0,children:"Paramètres"}),s&&a.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>l(""),children:s}),d&&a.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>u(""),children:d}),a.jsx(c,{sx:{mb:3},children:a.jsxs(it,{value:n,onChange:(e,t)=>{r(t)},"aria-label":"settings tabs",children:[a.jsx(rt,{label:"Entreprise",icon:a.jsx(pt,{})}),a.jsx(rt,{label:"Général",icon:a.jsx(de,{})}),a.jsx(rt,{label:"Impression",icon:a.jsx(Be,{})}),a.jsx(rt,{label:"Catégories",icon:a.jsx(nt,{})}),a.jsx(rt,{label:"Sauvegarde",icon:a.jsx(dt,{})}),a.jsx(rt,{label:"Données CSV",icon:a.jsx(Ue,{})})]})}),a.jsx(gr,{value:n,index:0,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Informations de l'Entreprise",avatar:a.jsx(pt,{})}),a.jsx(te,{children:a.jsxs(ce,{container:!0,spacing:3,children:[a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Nom de l'entreprise",value:m.nom,onChange:e=>g({...m,nom:e.target.value}),disabled:!k.canManageSettings})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Email",type:"email",value:m.email,onChange:e=>g({...m,email:e.target.value}),disabled:!k.canManageSettings})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Téléphone",value:m.telephone,onChange:e=>g({...m,telephone:e.target.value}),disabled:!k.canManageSettings})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Adresse",value:m.adresse,onChange:e=>g({...m,adresse:e.target.value}),disabled:!k.canManageSettings})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"RCCM",value:m.rccm||"",onChange:e=>g({...m,rccm:e.target.value}),disabled:!k.canManageSettings,helperText:"Registre de Commerce et du Crédit Mobilier"})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"ID NAT",value:m.idNat||"",onChange:e=>g({...m,idNat:e.target.value}),disabled:!k.canManageSettings,helperText:"Identification Nationale"})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(xr,{value:m.logo||"",onChange:e=>g({...m,logo:e}),disabled:!k.canManageSettings})}),k.canManageSettings&&a.jsx(ce,{item:!0,xs:12,children:a.jsx(I,{variant:"contained",startIcon:a.jsx(xt,{}),onClick:async()=>{if(!e)return;const n={...e,entreprise:m};await mi.setSettings(n),t(n),l("Paramètres de l'entreprise sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),a.jsx(gr,{value:n,index:1,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Paramètres Généraux",avatar:a.jsx(de,{})}),a.jsx(te,{children:a.jsxs(ce,{container:!0,spacing:3,children:[a.jsx(ce,{item:!0,xs:12,children:a.jsx(Yi,{label:"Taux de change (1 USD = ? CDF)",value:j.tauxChangeUSDCDF,onChange:e=>D({...j,tauxChangeUSDCDF:e}),min:1e3,max:1e4,step:10,exchangeRate:j.tauxChangeUSDCDF,disabled:!k.canManageSettings,showSlider:!0,allowUSDInput:!1,helperText:"Définit le taux de conversion entre USD et CDF"})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Seuil de stock bas",type:"number",value:j.seuilStockBas,onChange:e=>D({...j,seuilStockBas:parseInt(e.target.value)||0}),disabled:!k.canManageSettings,helperText:"Alerte quand le stock est ≤ à cette valeur"})}),k.canManageSettings&&a.jsx(ce,{item:!0,xs:12,children:a.jsx(I,{variant:"contained",startIcon:a.jsx(xt,{}),onClick:async()=>{if(!e)return;if(j.tauxChangeUSDCDF<=0)return void u("Le taux de change doit être supérieur à 0");if(j.seuilStockBas<0)return void u("Le seuil de stock bas ne peut pas être négatif");const n={...e,tauxChangeUSDCDF:j.tauxChangeUSDCDF,seuilStockBas:j.seuilStockBas};await mi.setSettings(n),t(n),l("Paramètres généraux sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),a.jsx(gr,{value:n,index:2,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Paramètres d'Impression",avatar:a.jsx(Be,{})}),a.jsx(te,{children:a.jsxs(ce,{container:!0,spacing:3,children:[a.jsxs(ce,{item:!0,xs:12,children:[a.jsx(Xe,{control:a.jsx(Ke,{checked:S.impressionAutomatique,onChange:e=>v({...S,impressionAutomatique:e.target.checked}),disabled:!k.canManageSettings}),label:"Impression automatique des reçus"}),a.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Lorsque activé, les reçus seront automatiquement imprimés après chaque vente ou dépense"})]}),a.jsxs(ce,{item:!0,xs:12,md:6,children:[a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Taille du papier"}),a.jsxs(Se,{value:S.taillePapier,label:"Taille du papier",onChange:e=>v({...S,taillePapier:e.target.value}),disabled:!k.canManageSettings,children:[a.jsx(J,{value:"thermal",children:"Reçu thermique (80mm)"}),a.jsx(J,{value:"a4",children:"A4"})]})]}),a.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Choisissez le format de papier pour l'impression des reçus"})]}),k.canManageSettings&&a.jsx(ce,{item:!0,xs:12,children:a.jsx(I,{variant:"contained",startIcon:a.jsx(xt,{}),onClick:async()=>{if(!e)return;const n={...e,impression:{impressionAutomatique:S.impressionAutomatique,taillePapier:S.taillePapier}};await mi.setSettings(n),t(n),l("Paramètres d'impression sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),a.jsx(gr,{value:n,index:3,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Gestion des Catégories",avatar:a.jsx(nt,{}),action:k.canManageSettings&&a.jsx(I,{variant:"contained",startIcon:a.jsx(Pe,{}),onClick:()=>M(),children:"Nouvelle Catégorie"})}),a.jsx(te,{children:a.jsx(p,{children:C.map((n,r)=>a.jsxs(ft.Fragment,{children:[a.jsxs(x,{children:[a.jsx(i,{sx:{width:20,height:20,backgroundColor:n.couleur,borderRadius:1,mr:2}}),a.jsx(y,{primary:n.nom,secondary:n.description}),k.canManageSettings&&a.jsxs(V,{children:[a.jsx(U,{edge:"end",onClick:()=>M(n),sx:{mr:1},children:a.jsx(Re,{})}),a.jsx(U,{edge:"end",color:"error",onClick:()=>(async n=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${n.nom}" ?`)){if((await mi.getProducts()).some(e=>e.categorie===n.nom))return u("Cette catégorie est utilisée par des produits et ne peut pas être supprimée"),void setTimeout(()=>u(""),5e3);const a=C.filter(e=>e.id!==n.id);if(b(a),e){const n={...e,categories:a};await mi.setSettings(n),t(n)}l("Catégorie supprimée avec succès"),setTimeout(()=>l(""),3e3)}})(n),children:a.jsx(q,{})})]})]}),r<C.length-1&&a.jsx(h,{})]},n.id))})})]})}),a.jsx(gr,{value:n,index:4,children:a.jsxs(ce,{container:!0,spacing:3,children:[a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Sauvegarde des Données",avatar:a.jsx(Ue,{})}),a.jsxs(te,{children:[a.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Exportez toutes vos données dans un fichier JSON pour créer une sauvegarde."}),a.jsx(I,{variant:"contained",startIcon:a.jsx(Ue,{}),onClick:async()=>{try{const{csvImportExportService:e}=await ba(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>hr);return{csvImportExportService:e}},void 0,import.meta.url),t=await e.exportAllData();if(t.success&&t.data){const e=new Blob([t.data],{type:"text/plain;charset=utf-8"}),n=URL.createObjectURL(e),a=document.createElement("a");a.href=n,a.download=`SmartBoutique_Backup_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(n),l("Sauvegarde CSV exportée avec succès (compatible Excel)"),setTimeout(()=>l(""),3e3)}else u(t.message||"Erreur lors de l'exportation"),setTimeout(()=>u(""),3e3)}catch(e){u("Erreur lors de l'exportation des données"),setTimeout(()=>u(""),3e3)}},fullWidth:!0,sx:{mt:2},children:"Exporter les Données"})]})]})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Restauration des Données",avatar:a.jsx(Te,{})}),a.jsxs(te,{children:[a.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Importez un fichier de sauvegarde pour restaurer vos données."}),a.jsx("input",{accept:".json",style:{display:"none"},id:"import-file",type:"file",onChange:async e=>{var t;const n=null==(t=e.target.files)?void 0:t[0];if(!n)return;const a=new FileReader;a.onload=async e=>{var t;try{const n=JSON.parse(null==(t=e.target)?void 0:t.result);await mi.importData(n)?(R(),l("Données importées avec succès"),setTimeout(()=>l(""),3e3),setTimeout(()=>window.location.reload(),2e3)):(u("Erreur lors de l'importation des données"),setTimeout(()=>u(""),3e3))}catch(n){u("Fichier de sauvegarde invalide"),setTimeout(()=>u(""),3e3)}},a.readAsText(n),e.target.value=""}}),a.jsx("label",{htmlFor:"import-file",children:a.jsx(I,{variant:"outlined",component:"span",startIcon:a.jsx(Te,{}),fullWidth:!0,sx:{mt:2},children:"Importer les Données"})})]})]})}),hi.hasRole(["super_admin"])&&a.jsx(ce,{item:!0,xs:12,children:a.jsxs(ee,{children:[a.jsx(ue,{title:"Réinitialisation",avatar:a.jsx(gt,{})}),a.jsxs(te,{children:[a.jsx(O,{severity:"warning",sx:{mb:2},children:a.jsxs(o,{variant:"body2",children:[a.jsx("strong",{children:"Attention:"})," Cette action supprimera toutes les données et restaurera les paramètres par défaut. Cette action est irréversible."]})}),a.jsx(I,{variant:"outlined",color:"error",startIcon:a.jsx(gt,{}),onClick:async()=>{if(!window.confirm("Êtes-vous sûr de vouloir réinitialiser toutes les données ? Cette action supprimera définitivement :"))return;if(window.confirm("ATTENTION: Cette action va supprimer TOUTES les données suivantes :\n\n• Tous les produits et inventaire\n• Toutes les ventes et transactions\n• Toutes les dettes et paiements\n• Tous les employés et leurs paiements\n• Toutes les dépenses\n• Tous les utilisateurs (sauf admin système)\n• Tous les fichiers CSV et données importées\n\nL'application sera nettoyée et prête pour vos propres données.\n\nCette action est IRRÉVERSIBLE. Confirmez-vous ?"))try{u(""),l("Réinitialisation en cours... Veuillez patienter.");const e=await mi.getCurrentUser(),t=(null==e?void 0:e.email)||"<EMAIL>";await B(),await L(),localStorage.setItem("smartboutique_data_reset","true");const n=await mi.getUsers(),a=n.find(e=>e.email===t)||n.find(e=>"super_admin"===e.role);a&&await mi.setCurrentUser(a),l("✅ Données réinitialisées avec succès ! Application prête pour vos données."),setTimeout(()=>{window.location.reload()},2e3)}catch(e){console.error("Erreur lors de la réinitialisation:",e),u("Erreur lors de la réinitialisation des données. Veuillez réessayer."),setTimeout(()=>u(""),5e3)}},children:"Réinitialiser toutes les Données"})]})]})})]})}),a.jsx(gr,{value:n,index:5,children:a.jsx(pr,{onSuccess:e=>{l(e),setTimeout(()=>l(""),3e3)},onError:e=>{u(e),setTimeout(()=>u(""),5e3)}})}),a.jsxs(Ae,{open:f,onClose:N,maxWidth:"sm",fullWidth:!0,children:[a.jsx(Ie,{children:E?"Modifier la Catégorie":"Nouvelle Catégorie"}),a.jsxs(Ne,{children:[d&&a.jsx(O,{severity:"error",sx:{mb:2},children:d}),a.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Nom de la catégorie *",value:F.nom,onChange:e=>T({...F,nom:e.target.value})})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Description",multiline:!0,rows:2,value:F.description,onChange:e=>T({...F,description:e.target.value})})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Couleur",type:"color",value:F.couleur,onChange:e=>T({...F,couleur:e.target.value}),InputProps:{startAdornment:a.jsx(ae,{position:"start",children:a.jsx(yt,{})})}})}),a.jsx(ce,{item:!0,xs:12,children:a.jsxs(i,{display:"flex",alignItems:"center",gap:1,children:[a.jsx(o,{variant:"body2",children:"Aperçu:"}),a.jsx(A,{label:F.nom||"Nom de la catégorie",sx:{backgroundColor:F.couleur,color:"white"}})]})})]})]}),a.jsxs(Le,{children:[a.jsx(I,{onClick:N,children:"Annuler"}),a.jsx(I,{onClick:async()=>{if(!F.nom.trim())return void u("Le nom de la catégorie est requis");if(C.some(e=>e.nom.toLowerCase()===F.nom.trim().toLowerCase()&&e.id!==(null==E?void 0:E.id)))return void u("Une catégorie avec ce nom existe déjà");let n;if(E)n=C.map(e=>e.id===E.id?{...e,nom:F.nom.trim(),description:F.description.trim(),couleur:F.couleur}:e);else{const e={id:Date.now().toString(),nom:F.nom.trim(),description:F.description.trim(),couleur:F.couleur};n=[...C,e]}if(b(n),e){const a={...e,categories:n};await mi.setSettings(a),t(a)}l(E?"Catégorie mise à jour":"Catégorie créée"),setTimeout(()=>l(""),3e3),N()},variant:"contained",children:E?"Mettre à jour":"Créer"})]})]})]}):a.jsx(o,{children:"Chargement..."})},jr=()=>{const[e,t]=Ct.useState([]),[n,r]=Ct.useState([]),[s,l]=Ct.useState(""),[d,u]=Ct.useState(""),[m,h]=Ct.useState(""),[p,x]=Ct.useState(0),[g,y]=Ct.useState(10),[j,D]=Ct.useState(!1),[S,v]=Ct.useState(null),[C,b]=Ct.useState({nomEmploye:"",montantCDF:0,datePaiement:Ut(new Date,"yyyy-MM-dd"),methodePaiement:"cash",notes:""}),[f,w]=Ct.useState(""),[E,P]=Ct.useState(""),[T,k]=Ct.useState({tauxChangeUSDCDF:2800}),[R,M]=Ct.useState(!1),[N,L]=Ct.useState([]),[V,B]=Ct.useState(!1),[_,$]=Ct.useState(null),[z,W]=Ct.useState({nomComplet:"",poste:"",salaireCDF:0,dateEmbauche:Ut(new Date,"yyyy-MM-dd"),telephone:"",adresse:"",statut:"actif",notes:""}),[X,Q]=Ct.useState(0),H=hi.getUserPermissions(),Y=hi.getCurrentUser(),K=[{value:"cash",label:"Cash"},{value:"mobile_money",label:"Mobile Money"},{value:"bank",label:"Banque"}];Ct.useEffect(()=>{Z(),ie(),re()},[]),Ct.useEffect(()=>{se()},[e,s,d,m]);const Z=async()=>{try{M(!0);const e=await mi.getEmployeePayments();t(e)}catch(e){console.error("Error loading employee payments:",e),w("Erreur lors du chargement des paiements employés")}finally{M(!1)}},ie=async()=>{try{const e=await mi.getEmployees();L(e)}catch(e){console.error("Error loading employees:",e),w("Erreur lors du chargement des employés")}},re=async()=>{try{const e=await mi.getSettings();k(e)}catch(e){console.error("Error loading settings:",e)}},se=()=>{let t=[...e];if(s&&(t=t.filter(e=>{var t;return e.nomEmploye.toLowerCase().includes(s.toLowerCase())||(null==(t=e.notes)?void 0:t.toLowerCase().includes(s.toLowerCase()))})),d&&(t=t.filter(e=>e.methodePaiement===d)),m){const e=new Date;let n,a=e;switch(m){case"today":n=new Date(e.getFullYear(),e.getMonth(),e.getDate());break;case"week":n=new Date(e.getTime()-6048e5);break;case"month":n=It(e),a=Nt(e);break;case"year":n=new Date(e.getFullYear(),0,1);break;default:n=new Date(0)}t=t.filter(e=>{const t=new Date(e.datePaiement);return Ot(t,{start:n,end:a})})}r(t),x(0)},oe=e=>{e?(v(e),b({nomEmploye:e.nomEmploye,montantCDF:e.montantCDF,datePaiement:e.datePaiement,methodePaiement:e.methodePaiement,notes:e.notes||""})):(v(null),b({nomEmploye:"",montantCDF:0,datePaiement:Ut(new Date,"yyyy-MM-dd"),methodePaiement:"cash",notes:""})),D(!0),w(""),P("")},le=()=>{D(!1),v(null),w(""),P("")},de=e=>{const t=K.find(t=>t.value===e);return t?t.label:e},ue=e=>{e?($(e),W({nomComplet:e.nomComplet,poste:e.poste,salaireCDF:e.salaireCDF,dateEmbauche:e.dateEmbauche,telephone:e.telephone||"",adresse:e.adresse||"",statut:e.statut,notes:e.notes||""})):($(null),W({nomComplet:"",poste:"",salaireCDF:0,dateEmbauche:Ut(new Date,"yyyy-MM-dd"),telephone:"",adresse:"",statut:"actif",notes:""})),B(!0),w(""),P("")},ve=()=>{B(!1),$(null),w(""),P("")},Ce=n.reduce((e,t)=>e+t.montantCDF,0);return n.reduce((e,t)=>e+(t.montantUSD||0),0),a.jsxs(i,{p:3,children:[a.jsxs(o,{variant:"h4",gutterBottom:!0,children:[a.jsx(G,{sx:{mr:1,verticalAlign:"middle"}}),"Gestion des Employés et Paiements"]}),a.jsx(c,{sx:{mb:3},children:a.jsxs(it,{value:X,onChange:(e,t)=>Q(t),children:[a.jsx(rt,{label:"Paiements Employés"}),a.jsx(rt,{label:"Gestion des Employés"})]})}),0===X&&a.jsxs(a.Fragment,{children:[a.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsxs(te,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Total Paiements"}),a.jsx(o,{variant:"h5",children:n.length})]})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsxs(te,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Montant Total"}),a.jsxs(o,{variant:"h5",fontWeight:"medium",children:[_i(Ce,T.tauxChangeUSDCDF).primaryAmount," ",_i(Ce,T.tauxChangeUSDCDF).primaryCurrency]}),a.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",_i(Ce,T.tauxChangeUSDCDF).secondaryAmount]})]})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsxs(te,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Employés Payés"}),a.jsx(o,{variant:"h5",children:new Set(n.map(e=>e.nomEmploye)).size})]})})})]}),f&&a.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>w(""),children:f}),E&&a.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>P(""),children:E}),a.jsx(c,{sx:{p:2,mb:3},children:a.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ne,{fullWidth:!0,label:"Rechercher employé",value:s,onChange:e=>l(e.target.value),InputProps:{startAdornment:a.jsx(ae,{position:"start",children:a.jsx(Fe,{})})}})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Méthode de paiement"}),a.jsxs(Se,{value:d,onChange:e=>u(e.target.value),label:"Méthode de paiement",children:[a.jsx(J,{value:"",children:"Toutes"}),K.map(e=>a.jsx(J,{value:e.value,children:e.label},e.value))]})]})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Période"}),a.jsx(Se,{value:m,onChange:e=>h(e.target.value),label:"Période",children:[{value:"",label:"Toutes les dates"},{value:"today",label:"Aujourd'hui"},{value:"week",label:"Cette semaine"},{value:"month",label:"Ce mois"},{value:"year",label:"Cette année"}].map(e=>a.jsx(J,{value:e.value,children:e.label},e.value))})]})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(I,{variant:"contained",startIcon:a.jsx(Pe,{}),onClick:()=>oe(),disabled:!H.canManageEmployeePayments||R,fullWidth:!0,children:"Nouveau Paiement"})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:a.jsx(I,{variant:"outlined",onClick:()=>{l(""),u(""),h("")},fullWidth:!0,children:"Réinitialiser"})})]})}),a.jsxs(c,{children:[a.jsx(me,{children:a.jsxs(he,{children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:"Employé"}),a.jsx(ge,{children:"Montant (CDF)"}),a.jsx(ge,{children:"Montant (USD)"}),a.jsx(ge,{children:"Date de Paiement"}),a.jsx(ge,{children:"Méthode"}),a.jsx(ge,{children:"Notes"}),a.jsx(ge,{children:"Créé par"}),H.canManageEmployeePayments&&a.jsx(ge,{children:"Actions"})]})}),a.jsx(ye,{children:R?a.jsx(xe,{children:a.jsx(ge,{colSpan:8,align:"center",children:a.jsx(qe,{})})}):0===n.length?a.jsx(xe,{children:a.jsx(ge,{colSpan:8,align:"center",children:a.jsx(o,{variant:"body2",color:"textSecondary",children:"Aucun paiement employé trouvé"})})}):n.slice(p*g,p*g+g).map(e=>a.jsxs(xe,{children:[a.jsx(ge,{children:a.jsxs(i,{display:"flex",alignItems:"center",children:[a.jsx(lt,{sx:{mr:1,color:"primary.main"}}),e.nomEmploye]})}),a.jsx(ge,{children:a.jsx(o,{variant:"body2",fontWeight:"bold",color:"primary",children:Bi(e.montantCDF,"CDF")})}),a.jsx(ge,{children:a.jsx(o,{variant:"body2",color:"textSecondary",children:Bi(e.montantUSD||0,"USD")})}),a.jsx(ge,{children:a.jsxs(i,{display:"flex",alignItems:"center",children:[a.jsx(at,{sx:{mr:1,fontSize:16,color:"text.secondary"}}),Ut(new Date(e.datePaiement),"dd/MM/yyyy",{locale:Di})]})}),a.jsx(ge,{children:a.jsx(A,{label:de(e.methodePaiement),size:"small",color:"cash"===e.methodePaiement?"success":"mobile_money"===e.methodePaiement?"info":"default"})}),a.jsx(ge,{children:a.jsx(o,{variant:"body2",noWrap:!0,sx:{maxWidth:150},children:e.notes||"-"})}),a.jsx(ge,{children:a.jsx(o,{variant:"body2",color:"textSecondary",children:e.creePar})}),H.canManageEmployeePayments&&a.jsxs(ge,{children:[a.jsx(F,{title:"Modifier",children:a.jsx(U,{size:"small",onClick:()=>oe(e),disabled:R,children:a.jsx(Re,{})})}),a.jsx(F,{title:"Supprimer",children:a.jsx(U,{size:"small",onClick:()=>(async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer le paiement de ${e.nomEmploye} ?`))try{M(!0),await mi.deleteEmployeePayment(e.id),P("Paiement employé supprimé avec succès"),await Z()}catch(t){console.error("Error deleting employee payment:",t),w("Erreur lors de la suppression du paiement employé")}finally{M(!1)}})(e),disabled:R,color:"error",children:a.jsx(q,{})})})]})]},e.id))})]})}),a.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100],component:"div",count:n.length,rowsPerPage:g,page:p,onPageChange:(e,t)=>{x(t)},onRowsPerPageChange:e=>{y(parseInt(e.target.value,10)),x(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),a.jsxs(Ae,{open:j,onClose:le,maxWidth:"sm",fullWidth:!0,children:[a.jsx(Ie,{children:S?"Modifier le Paiement Employé":"Nouveau Paiement Employé"}),a.jsx(Ne,{children:a.jsx(i,{sx:{pt:1},children:a.jsxs(ce,{container:!0,spacing:2,children:[a.jsx(ce,{item:!0,xs:12,children:a.jsxs(je,{fullWidth:!0,disabled:R,children:[a.jsx(De,{id:"employee-select-label",children:"Nom de l'employé *"}),a.jsxs(Se,{labelId:"employee-select-label",value:C.nomEmploye,onChange:e=>b({...C,nomEmploye:e.target.value}),label:"Nom de l'employé *",startAdornment:a.jsx(ae,{position:"start",children:a.jsx(lt,{})}),children:[a.jsx(J,{value:"",disabled:!0,children:a.jsx("em",{children:"Sélectionner un employé"})}),N.filter(e=>"actif"===e.statut).sort((e,t)=>e.nomComplet.localeCompare(t.nomComplet,"fr",{sensitivity:"base"})).map(e=>a.jsx(J,{value:e.nomComplet,children:a.jsxs(i,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start"},children:[a.jsx(o,{variant:"body1",sx:{fontWeight:500},children:e.nomComplet}),a.jsx(o,{variant:"caption",color:"text.secondary",children:e.poste})]})},e.id)),0===N.filter(e=>"actif"===e.statut).length&&a.jsx(J,{value:"",disabled:!0,children:a.jsx("em",{children:"Aucun employé actif disponible"})})]})]})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(Yi,{label:"Montant du paiement *",value:C.montantCDF,onChange:e=>b({...C,montantCDF:e}),currency:"CDF",disabled:R,fullWidth:!0})}),a.jsx(ce,{item:!0,xs:12,sm:6,children:a.jsx(ne,{fullWidth:!0,type:"date",label:"Date de paiement *",value:C.datePaiement,onChange:e=>b({...C,datePaiement:e.target.value}),disabled:R,InputLabelProps:{shrink:!0},InputProps:{startAdornment:a.jsx(ae,{position:"start",children:a.jsx(at,{})})}})}),a.jsx(ce,{item:!0,xs:12,sm:6,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Méthode de paiement *"}),a.jsx(Se,{value:C.methodePaiement,onChange:e=>b({...C,methodePaiement:e.target.value}),label:"Méthode de paiement *",disabled:R,children:K.map(e=>a.jsx(J,{value:e.value,children:e.label},e.value))})]})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,multiline:!0,rows:3,label:"Notes (optionnel)",value:C.notes,onChange:e=>b({...C,notes:e.target.value}),disabled:R,placeholder:"Ajoutez des notes sur ce paiement..."})}),C.montantCDF>0&&a.jsx(ce,{item:!0,xs:12,children:a.jsxs(i,{sx:{p:2,bgcolor:"grey.50",borderRadius:1},children:[a.jsxs(o,{variant:"body2",color:"textSecondary",gutterBottom:!0,children:["Équivalent USD (taux: ",T.tauxChangeUSDCDF," CDF/USD)"]}),a.jsx(o,{variant:"h6",color:"primary",children:Bi(C.montantCDF/T.tauxChangeUSDCDF,"USD")})]})})]})})}),a.jsxs(Le,{children:[a.jsx(I,{onClick:le,disabled:R,children:"Annuler"}),a.jsx(I,{onClick:async()=>{var e;try{if(M(!0),w(""),!C.nomEmploye.trim())return void w("Veuillez sélectionner un employé");if(!N.find(e=>e.nomComplet===C.nomEmploye&&"actif"===e.statut))return void w("L'employé sélectionné n'est pas valide ou n'est plus actif");const t=Ai(C.montantCDF,"Le montant du paiement",{allowZero:!1,allowNegative:!1});if(!t.isValid)return void w(t.errors[0]||Ni);const n={id:(null==S?void 0:S.id)||`emp_pay_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,nomEmploye:C.nomEmploye.trim(),montantCDF:C.montantCDF,montantUSD:C.montantCDF/T.tauxChangeUSDCDF,datePaiement:C.datePaiement,methodePaiement:C.methodePaiement,notes:null==(e=C.notes)?void 0:e.trim(),creePar:(null==Y?void 0:Y.nom)||"Système",dateCreation:(null==S?void 0:S.dateCreation)||(new Date).toISOString(),dateModification:S?(new Date).toISOString():void 0};S?(await mi.updateEmployeePayment(n),P("Paiement employé modifié avec succès")):(await mi.addEmployeePayment(n),P("Paiement employé ajouté avec succès")),await Z(),le()}catch(t){console.error("Error saving employee payment:",t),w("Erreur lors de la sauvegarde du paiement employé")}finally{M(!1)}},variant:"contained",disabled:R||!C.nomEmploye.trim()||C.montantCDF<=0||!N.find(e=>e.nomComplet===C.nomEmploye&&"actif"===e.statut),startIcon:R?a.jsx(qe,{size:20}):a.jsx(G,{}),children:S?"Modifier":"Ajouter"})]})]})]}),1===X&&a.jsxs(a.Fragment,{children:[a.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsxs(te,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Total Employés"}),a.jsx(o,{variant:"h5",children:N.length})]})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsxs(te,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Employés Actifs"}),a.jsx(o,{variant:"h5",children:N.filter(e=>"actif"===e.statut).length})]})})}),a.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:a.jsx(ee,{children:a.jsxs(te,{children:[a.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Masse Salariale (CDF)"}),a.jsx(o,{variant:"h5",children:Bi(N.filter(e=>"actif"===e.statut).reduce((e,t)=>e+t.salaireCDF,0),"CDF")})]})})})]}),a.jsx(c,{sx:{p:2,mb:3},children:a.jsxs(i,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[a.jsx(o,{variant:"h6",children:"Liste des Employés"}),H.canManageEmployeePayments&&a.jsx(I,{variant:"contained",startIcon:a.jsx(Pe,{}),onClick:()=>ue(),children:"Ajouter Employé"})]})}),a.jsx(c,{children:a.jsx(me,{children:a.jsxs(he,{children:[a.jsx(pe,{children:a.jsxs(xe,{children:[a.jsx(ge,{children:"Nom"}),a.jsx(ge,{children:"Poste"}),a.jsx(ge,{children:"Salaire (CDF)"}),a.jsx(ge,{children:"Date d'Embauche"}),a.jsx(ge,{children:"Statut"}),a.jsx(ge,{children:"Téléphone"}),H.canManageEmployeePayments&&a.jsx(ge,{children:"Actions"})]})}),a.jsx(ye,{children:0===N.length?a.jsx(xe,{children:a.jsx(ge,{colSpan:7,align:"center",children:a.jsx(o,{variant:"body2",color:"textSecondary",children:"Aucun employé trouvé"})})}):N.map(e=>a.jsxs(xe,{children:[a.jsx(ge,{children:a.jsxs(i,{display:"flex",alignItems:"center",children:[a.jsx(lt,{sx:{mr:1,color:"primary.main"}}),e.nomComplet," "]})}),a.jsx(ge,{children:e.poste}),a.jsx(ge,{children:a.jsx(o,{variant:"body2",fontWeight:"bold",color:"primary",children:Bi(e.salaireCDF,"CDF")})}),a.jsx(ge,{children:Ut(new Date(e.dateEmbauche),"dd/MM/yyyy",{locale:Di})}),a.jsx(ge,{children:a.jsx(A,{label:e.statut,color:"actif"===e.statut?"success":"default",size:"small"})}),a.jsx(ge,{children:e.telephone||"-"}),H.canManageEmployeePayments&&a.jsx(ge,{children:a.jsxs(i,{display:"flex",gap:1,children:[a.jsx(F,{title:"Modifier",children:a.jsx(U,{size:"small",onClick:()=>ue(e),children:a.jsx(Re,{fontSize:"small"})})}),a.jsx(F,{title:"Supprimer",children:a.jsx(U,{size:"small",color:"error",onClick:()=>(async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer l'employé ${e.nomComplet} ?`))try{M(!0),await mi.deleteEmployee(e.id),P("Employé supprimé avec succès"),await ie()}catch(t){console.error("Error deleting employee:",t),w("Erreur lors de la suppression de l'employé")}finally{M(!1)}})(e),children:a.jsx(q,{fontSize:"small"})})})]})})]},e.id))})]})})}),a.jsxs(Ae,{open:V,onClose:ve,maxWidth:"md",fullWidth:!0,children:[a.jsx(Ie,{children:_?"Modifier Employé":"Ajouter Employé"}),a.jsxs(Ne,{children:[f&&a.jsx(O,{severity:"error",sx:{mb:2},children:f}),E&&a.jsx(O,{severity:"success",sx:{mb:2},children:E}),a.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Nom Complet *",value:z.nomComplet,onChange:e=>W({...z,nomComplet:e.target.value}),placeholder:"Ex: Jean Dupont"})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Poste *",value:z.poste,onChange:e=>W({...z,poste:e.target.value})})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(Yi,{label:"Salaire",value:z.salaireCDF,onChange:e=>W({...z,salaireCDF:e}),min:0,max:1e7,step:1e3,exchangeRate:T.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Date d'Embauche",type:"date",value:z.dateEmbauche,onChange:e=>W({...z,dateEmbauche:e.target.value}),InputLabelProps:{shrink:!0}})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsxs(je,{fullWidth:!0,children:[a.jsx(De,{children:"Statut"}),a.jsxs(Se,{value:z.statut,label:"Statut",onChange:e=>W({...z,statut:e.target.value}),children:[a.jsx(J,{value:"actif",children:"Actif"}),a.jsx(J,{value:"inactif",children:"Inactif"}),a.jsx(J,{value:"suspendu",children:"Suspendu"})]})]})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Téléphone",value:z.telephone,onChange:e=>W({...z,telephone:e.target.value})})}),a.jsx(ce,{item:!0,xs:12,md:6,children:a.jsx(ne,{fullWidth:!0,label:"Adresse",value:z.adresse,onChange:e=>W({...z,adresse:e.target.value})})}),a.jsx(ce,{item:!0,xs:12,children:a.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:3,value:z.notes,onChange:e=>W({...z,notes:e.target.value})})})]})]}),a.jsxs(Le,{children:[a.jsx(I,{onClick:ve,children:"Annuler"}),a.jsx(I,{onClick:async()=>{var e,t,n;if(z.nomComplet.trim()&&z.poste.trim())try{M(!0);const a=(new Date).toISOString(),i={id:(null==_?void 0:_.id)||`emp_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,nomComplet:z.nomComplet.trim(),poste:z.poste.trim(),salaireCDF:z.salaireCDF,salaireUSD:z.salaireCDF/T.tauxChangeUSDCDF,dateEmbauche:z.dateEmbauche,telephone:null==(e=z.telephone)?void 0:e.trim(),adresse:null==(t=z.adresse)?void 0:t.trim(),statut:z.statut,notes:null==(n=z.notes)?void 0:n.trim(),creePar:(null==Y?void 0:Y.nom)||"Système",dateCreation:(null==_?void 0:_.dateCreation)||a,dateModification:_?a:void 0};_?(await mi.updateEmployee(i),P("Employé modifié avec succès")):(await mi.addEmployee(i),P("Employé ajouté avec succès")),await ie(),ve()}catch(a){console.error("Error saving employee:",a),w("Erreur lors de la sauvegarde de l'employé")}finally{M(!1)}else w("Le nom complet et le poste sont requis")},variant:"contained",disabled:R||!z.nomComplet.trim()||!z.poste.trim(),startIcon:R?a.jsx(qe,{size:20}):a.jsx(lt,{}),children:_?"Modifier":"Ajouter"})]})]})]})]})},Dr=()=>{var e,t,n,a,i,r,s,o,l;const c=(()=>{const e=Na();return{components:{MuiButton:{styleOverrides:{root:{minHeight:e.isMobile?48:36,fontSize:e.isMobile?"1rem":"0.875rem"}}},MuiIconButton:{styleOverrides:{root:{padding:e.isMobile?12:8}}},MuiTableCell:{styleOverrides:{root:{padding:e.isMobile?"12px 8px":"16px"}}}}}})();return St({palette:{primary:{main:"#1976d2"},secondary:{main:"#dc004e"},background:{default:"#f5f5f5"}},typography:{fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',h4:{fontWeight:600},h5:{fontWeight:600},h6:{fontWeight:600}},components:{MuiButton:{styleOverrides:{root:{textTransform:"none",...null==(n=null==(t=null==(e=c.components)?void 0:e.MuiButton)?void 0:t.styleOverrides)?void 0:n.root}}},MuiCard:{styleOverrides:{root:{boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}}},MuiIconButton:{styleOverrides:{root:{...null==(r=null==(i=null==(a=c.components)?void 0:a.MuiIconButton)?void 0:i.styleOverrides)?void 0:r.root}}},MuiTableCell:{styleOverrides:{root:{...null==(l=null==(o=null==(s=c.components)?void 0:s.MuiTableCell)?void 0:o.styleOverrides)?void 0:l.root}}}}},va)},Sr=()=>{const[e,t]=Ct.useState(null),[n,r]=Ct.useState(!0),[s,o]=Ct.useState(null),[l,c]=Ct.useState(()=>Dr());Ct.useEffect(()=>{const e=()=>{document.querySelectorAll("input, textarea, [contenteditable]").forEach(e=>{const t=e;t.style.pointerEvents="auto",t.style.userSelect="text",(!t.hasAttribute("tabindex")||t.tabIndex<0)&&(t.tabIndex=0)})};e();const t=()=>{setTimeout(e,50)};return window.addEventListener("hashchange",t),()=>{window.removeEventListener("hashchange",t)}},[]),Ct.useEffect(()=>{(async()=>{try{0;if("true"===localStorage.getItem("smartboutique_data_reset")){if(0===(await mi.getUsers()).length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];await mi.setUsers(e)}0}else await mi.initializeDefaultData();Na().isMobile&&await mi.migrateFromDesktop(),await hi.initialize();const e=hi.getCurrentUser();t(e),r(!1)}catch(e){console.error("SmartBoutique: Error during initialization:",e),o(e instanceof Error?e.message:"Unknown error"),r(!1)}})()},[]);return s?a.jsxs(jt,{theme:l,children:[a.jsx(Dt,{}),a.jsxs(i,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",minHeight:"100vh",p:3,children:[a.jsx("h2",{children:"Erreur de chargement"}),a.jsx("p",{children:"Une erreur s'est produite lors du chargement de l'application:"}),a.jsx("pre",{style:{background:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:s}),a.jsx("button",{onClick:()=>window.location.reload(),children:"Recharger l'application"})]})]}):n?a.jsxs(jt,{theme:l,children:[a.jsx(Dt,{}),a.jsx(i,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"100vh",children:"Chargement..."})]}):a.jsxs(jt,{theme:l,children:[a.jsx(Dt,{}),a.jsx(ga,{children:a.jsxs(ha,{children:[a.jsx(ua,{path:"/login",element:e?a.jsx(ca,{to:"/dashboard",replace:!0}):a.jsx(Pi,{onLogin:e=>{t(e)}})}),a.jsxs(ua,{path:"/",element:a.jsx(wi,{children:a.jsx(fi,{currentUser:e,onLogout:async()=>{await hi.logout(),t(null)}})}),children:[a.jsx(ua,{index:!0,element:a.jsx(ca,{to:"/dashboard",replace:!0})}),a.jsx(ua,{path:"dashboard",element:a.jsx(wi,{requiredPermission:"canViewDashboard",children:a.jsx(Gi,{})})}),a.jsx(ua,{path:"products",element:a.jsx(wi,{requiredPermission:"canViewProducts",children:a.jsx(Zi,{})})}),a.jsx(ua,{path:"sales",element:a.jsx(wi,{requiredPermission:"canViewSales",children:a.jsx(ir,{})})}),a.jsx(ua,{path:"debts",element:a.jsx(wi,{requiredPermission:"canViewDebts",children:a.jsx(rr,{})})}),a.jsx(ua,{path:"expenses",element:a.jsx(wi,{requiredPermission:"canViewExpenses",children:a.jsx(or,{})})}),a.jsx(ua,{path:"employee-payments",element:a.jsx(wi,{requiredPermission:"canViewEmployeePayments",children:a.jsx(jr,{})})}),a.jsx(ua,{path:"reports",element:a.jsx(wi,{requiredPermission:"canViewReports",children:a.jsx(cr,{})})}),a.jsx(ua,{path:"users",element:a.jsx(wi,{requiredPermission:"canViewUsers",children:a.jsx(dr,{})})}),a.jsx(ua,{path:"settings",element:a.jsx(wi,{requiredPermission:"canViewSettings",children:a.jsx(yr,{})})})]}),a.jsx(ua,{path:"*",element:a.jsx(ca,{to:"/dashboard",replace:!0})})]})})]})};class vr extends Ct.Component{constructor(e){super(e),n(this,"handleReload",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0}),window.location.reload()}),n(this,"handleReset",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})}),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?a.jsx(i,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",minHeight:"100vh",p:3,bgcolor:"#f5f5f5",children:a.jsxs(c,{elevation:3,sx:{p:4,maxWidth:600,width:"100%"},children:[a.jsx(o,{variant:"h4",color:"error",gutterBottom:!0,children:"Oops! Une erreur s'est produite"}),a.jsx(o,{variant:"body1",paragraph:!0,children:"L'application a rencontré une erreur inattendue. Vous pouvez essayer de continuer ou recharger l'application."}),!1,a.jsxs(i,{display:"flex",gap:2,mt:3,children:[a.jsx(I,{variant:"contained",color:"primary",onClick:this.handleReset,children:"Continuer"}),a.jsx(I,{variant:"outlined",color:"secondary",onClick:this.handleReload,children:"Recharger l'application"})]})]})}):this.props.children}}Xt.register(Qt,Jt,Ht,Gt,Yt,Kt,Zt,en,tn);const Cr=document.getElementById("root");Cr?rn.createRoot(Cr).render(a.jsx(ft.StrictMode,{children:a.jsx(vr,{children:a.jsx(Sr,{})})})):console.error("SmartBoutique: Root element not found!");export{Ta as W};
